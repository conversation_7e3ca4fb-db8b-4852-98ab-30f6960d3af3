'use client';

import { useEffect, useState, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Users, Calendar, Hash } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

import {
  useTeams,
  useProjects,
  useMembers,
  useTeamMembers,
} from '@/hooks/use-db';
import type { Team, TeamMember } from '@/lib/supabase/database-modules';

interface TeamDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function TeamDetailPage({ params }: TeamDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { teams, loading, deleteTeam } = useTeams();
  const { projects } = useProjects();
  const { members } = useMembers();
  const { fetchTeamMembersByTeam } = useTeamMembers();

  const [team, setTeam] = useState<Team | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  // Helper functions for project status display
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'planning':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'on_hold':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatStatus = (status: string) => {
    return status
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  useEffect(() => {
    if (!loading && teams.length > 0) {
      const foundTeam = teams.find((t) => t.id === resolvedParams.id);
      setTeam(foundTeam || null);

      // Fetch team members when team is found
      if (foundTeam) {
        fetchTeamMembersByTeam(foundTeam.id)
          .then((members) => {
            setTeamMembers(members);
          })
          .catch((error) => {
            console.error('Error fetching team members:', error);
            setTeamMembers([]);
          });
      }
    }
  }, [teams, loading, resolvedParams.id, fetchTeamMembersByTeam]);

  const handleDeleteTeam = () => {
    if (!team) return;

    const confirmDelete = window.confirm(
      `Are you sure you want to delete the team "${team.name}"? This action cannot be undone.`
    );

    if (!confirmDelete) return;

    setIsDeleting(true);
    const teamName = team.name;

    const deletePromise = deleteTeam(team.id)
      .then(() => {
        router.push('/admin/teams');
      })
      .catch((error) => {
        throw error;
      })
      .finally(() => {
        setIsDeleting(false);
      });

    toast.promise(deletePromise, {
      loading: `Deleting team "${teamName}"...`,
      success: `Team "${teamName}" deleted successfully`,
      error: (error) =>
        `Failed to delete team: ${error?.message || 'Unknown error'}`,
    });
  };

  if (loading) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Team Details</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-muted-foreground'>Loading team details...</div>
          </div>
        </section>
      </main>
    );
  }

  if (!team) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Team Not Found</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-destructive'>Team not found</div>
          </div>
        </section>
      </main>
    );
  }

  // Get team-related projects
  const teamProjects = team?.projects
    ? team.projects
        .map((projectId) => {
          const project = projects.find((p) => p.id === projectId);
          return project
            ? {
                id: project.id,
                name: project.name,
                description: project.description,
                status:
                  typeof project.status === 'string'
                    ? project.status
                    : project.status?.name || 'unknown',
                lead_id: project.lead_id,
                target_date: project.target_date,
              }
            : null;
        })
        .filter(
          (project): project is NonNullable<typeof project> => project !== null
        )
    : [];

  // Get member details for team members
  const teamMembersWithDetails = teamMembers
    .map((teamMember) => {
      const memberDetails = members.find((m) => m.id === teamMember.user_id);
      return memberDetails
        ? {
            ...memberDetails,
            joined_team_at: teamMember.joined_at,
          }
        : null;
    })
    .filter((member): member is NonNullable<typeof member> => member !== null);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.back()}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>{team.name}</p>
        </div>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => router.push(`/admin/teams/${team.id}/edit`)}
          >
            <Edit className='h-4 w-4 mr-2' />
            Edit Team
          </Button>
          <Button
            variant='destructive'
            size='sm'
            onClick={handleDeleteTeam}
            disabled={isDeleting}
          >
            <Trash2 className='h-4 w-4 mr-2' />
            {isDeleting ? 'Deleting...' : 'Delete Team'}
          </Button>
        </div>
      </header>

      <ScrollArea className='flex-1 min-h-[calc(100vh-theme(spacing.24))]'>
        <section className='p-6 space-y-6'>
          {/* Team Header Card */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-3'>
                <div
                  className='w-12 h-12 flex items-center justify-center text-white font-semibold'
                  style={{ backgroundColor: team.color || '#3b82f6' }}
                >
                  {team.icon ? (
                    <span className='text-lg'>{team.icon}</span>
                  ) : (
                    <Users className='h-6 w-6' />
                  )}
                </div>
                <div>
                  <h1 className='text-2xl font-bold'>{team.name}</h1>
                  {team.description && (
                    <p className='text-muted-foreground font-normal'>
                      {team.description}
                    </p>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Created</p>
                    <p className='text-sm text-muted-foreground'>
                      {team.created_at
                        ? new Date(team.created_at).toLocaleDateString()
                        : 'Unknown'}
                    </p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Users className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Members</p>
                    <p className='text-sm text-muted-foreground'>
                      {teamMembersWithDetails.length} member
                      {teamMembersWithDetails.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Hash className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <p className='text-sm font-medium'>Projects</p>
                    <p className='text-sm text-muted-foreground'>
                      {teamProjects.length} project
                      {teamProjects.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Team Members Section */}
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
            </CardHeader>
            <CardContent>
              {teamMembersWithDetails.length > 0 ? (
                <div className='space-y-4'>
                  {teamMembersWithDetails.map((member) => (
                    <div
                      key={member.id}
                      className='flex items-center gap-4 p-3 border'
                    >
                      <Avatar className='h-10 w-10'>
                        <AvatarImage
                          src={member.avatar_url || ''}
                          alt={member.full_name || member.name}
                        />
                        <AvatarFallback>
                          {(member.full_name || member.name)
                            .split(' ')
                            .map((n) => n[0])
                            .join('')
                            .toUpperCase()
                            .slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className='flex-1'>
                        <p className='font-medium'>
                          {member.full_name || member.name}
                        </p>
                        <p className='text-sm text-muted-foreground'>
                          {member.email}
                        </p>
                        {member.joined_team_at && (
                          <p className='text-xs text-muted-foreground'>
                            Joined team{' '}
                            {new Date(
                              member.joined_team_at
                            ).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                      <div className='flex items-center gap-2'>
                        <Badge variant='secondary'>{member.role}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Users className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                  <p className='text-muted-foreground'>No team members yet</p>
                  <p className='text-sm text-muted-foreground'>
                    Add members to this team to get started
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Associated Projects Section */}
          <Card>
            <CardHeader>
              <CardTitle>Associated Projects</CardTitle>
            </CardHeader>
            <CardContent>
              {teamProjects.length > 0 ? (
                <div className='space-y-4'>
                  {teamProjects.map((project) => {
                    const projectLead = project.lead_id
                      ? members.find((m) => m.id === project.lead_id)
                      : null;

                    return (
                      <div
                        key={project.id}
                        className='flex items-center gap-4 p-3 border bg-card hover:bg-accent/50 transition-colors'
                      >
                        <div className='flex-1 space-y-1'>
                          <div className='flex items-center gap-2'>
                            <p className='font-medium'>{project.name}</p>
                            <Badge
                              variant='outline'
                              className={`text-xs ${getStatusColor(project.status)}`}
                            >
                              {formatStatus(project.status)}
                            </Badge>
                          </div>
                          {project.description && (
                            <p className='text-sm text-muted-foreground truncate'>
                              {project.description}
                            </p>
                          )}
                          <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                            {projectLead && (
                              <div className='flex items-center gap-1'>
                                <Avatar className='h-4 w-4'>
                                  <AvatarImage
                                    src={projectLead.avatar_url || ''}
                                  />
                                  <AvatarFallback className='text-xs'>
                                    {(projectLead.full_name || projectLead.name)
                                      .charAt(0)
                                      .toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <span>
                                  {projectLead.full_name || projectLead.name}
                                </span>
                              </div>
                            )}
                            {project.target_date && (
                              <div className='flex items-center gap-1'>
                                <Calendar className='h-3 w-3' />
                                <span>
                                  {new Date(
                                    project.target_date
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() =>
                            router.push(`/admin/projects/${project.id}`)
                          }
                        >
                          View Project
                        </Button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Hash className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                  <p className='text-muted-foreground'>
                    No projects associated
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    Associate projects with this team to organize work
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </section>
      </ScrollArea>
    </main>
  );
}
