'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useProjects, usePortfolio } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';

import { createCollaboratorProjectsColumns } from './columns';

interface CollaboratorProjectsTableProps {
  projects?: Project[];
  loading?: boolean;
}

export function CollaboratorProjectsTable({
  projects: externalProjects,
  loading: externalLoading,
}: CollaboratorProjectsTableProps = {}) {
  const { getCollaboratorProjects } = useProjects();
  const { portfolioData } = usePortfolio();
  const [internalProjects, setInternalProjects] = useState<Project[]>([]);
  const [internalLoading, setInternalLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const columns = createCollaboratorProjectsColumns();

  // Use external data if provided, otherwise fetch internally
  const projects = externalProjects || internalProjects;
  const loading =
    externalLoading !== undefined ? externalLoading : internalLoading;

  useEffect(() => {
    // Only fetch internally if no external data is provided
    if (externalProjects !== undefined || !portfolioData?.id) return;

    setInternalLoading(true);
    setError(null);

    getCollaboratorProjects(portfolioData.id)
      .then((collaboratorProjects) => {
        setInternalProjects(collaboratorProjects);
      })
      .catch((err) => {
        console.error('Error fetching collaborator projects:', err);
        setError('Failed to load projects');
        toast.error('Failed to load projects');
      })
      .finally(() => {
        setInternalLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorProjects, externalProjects]);

  if (error) {
    return (
      <div className='w-full p-6'>
        <div className='text-center text-red-500'>{error}</div>
      </div>
    );
  }

  return <DataTable columns={columns} data={projects} isLoading={loading} />;
}
