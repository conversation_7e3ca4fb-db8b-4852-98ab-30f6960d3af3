import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProjectPriorityChange } from '@/lib/emails/projects/priority-changed';
import type { ProjectPriorityChangeEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request) {
  return new Promise((resolve, reject) => {
    let emailData: ProjectPriorityChangeEmailData;

    request
      .json()
      .then((data: ProjectPriorityChangeEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.project ||
          !emailData.oldPriority ||
          !emailData.newPriority ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: project, oldPriority, newPriority, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = ProjectPriorityChange(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProjectPriorityChange(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Project Priority Changed: ${emailData.project.name}`,
          react: ProjectPriorityChange(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Project priority change email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Project Priority Changed: ${emailData.project.name}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Project priority change email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error:
              error.message || 'Failed to send project priority change email',
          })
        );
      });
  });
}
