'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { CollaboratorTasksMetrics } from '@/components/tasks/collaborator-tasks-metrics';
import { CollaboratorTasksTable } from '@/components/tables/collaborator-tasks/collaborator-tasks-table';
import { useTasks, usePortfolio } from '@/hooks/use-db';
import type { Task } from '@/lib/supabase/database-modules';

export default function CollaboratorTasksPage() {
  const { getCollaboratorTasks } = useTasks();
  const { portfolioData } = usePortfolio();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!portfolioData?.id) return;

    setLoading(true);

    getCollaboratorTasks(portfolioData.id)
      .then((collaboratorTasks) => {
        setTasks(collaboratorTasks);
      })
      .catch((error) => {
        console.error('Error fetching collaborator tasks:', error);
        toast.error('Failed to load tasks');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorTasks]);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>My Tasks</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <CollaboratorTasksMetrics tasks={tasks} loading={loading} />
        <CollaboratorTasksTable tasks={tasks} loading={loading} />
      </section>
    </main>
  );
}
