import type { ReferalProposalType } from './database-modules';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.12 (cd3cf9e)';
  };
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      affiliate_earnings: {
        Row: {
          amount: number;
          commission_rate: number;
          created_at: string;
          earned_date: string;
          id: string;
          paid_date: string | null;
          payment_id: string | null;
          reference_id: string | null;
          source: string;
          status: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          amount: number;
          commission_rate: number;
          created_at?: string;
          earned_date?: string;
          id?: string;
          paid_date?: string | null;
          payment_id?: string | null;
          reference_id?: string | null;
          source: string;
          status?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          amount?: number;
          commission_rate?: number;
          created_at?: string;
          earned_date?: string;
          id?: string;
          paid_date?: string | null;
          payment_id?: string | null;
          reference_id?: string | null;
          source?: string;
          status?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'affiliate_earnings_payment_id_fkey';
            columns: ['payment_id'];
            isOneToOne: false;
            referencedRelation: 'payments';
            referencedColumns: ['id'];
          },
        ];
      };
      affiliate_proposals: {
        Row: {
          affiliate_proposal: ReferalProposalType | null;
          completed: boolean | null;
          created_at: string;
          id: number;
          is_approved: boolean | null;
          is_recieved: boolean | null;
          user_email: string | null;
          user_id: string | null;
        };
        Insert: {
          affiliate_proposal?: ReferalProposalType | null;
          completed?: boolean | null;
          created_at?: string;
          id?: number;
          is_approved?: boolean | null;
          is_recieved?: boolean | null;
          user_email?: string | null;
          user_id?: string | null;
        };
        Update: {
          affiliate_proposal?: ReferalProposalType | null;
          completed?: boolean | null;
          created_at?: string;
          id?: number;
          is_approved?: boolean | null;
          is_recieved?: boolean | null;
          user_email?: string | null;
          user_id?: string | null;
        };
        Relationships: [];
      };
      budgets: {
        Row: {
          ActualAmount: number;
          AffiliateCommission: number | null;
          affiliateId: string | null;
          ApprovalDate: string | null;
          ApprovedBy: string | null;
          Category: string;
          ClientId: string | null;
          collaborators: Json | null;
          created_at: string;
          Currency: string;
          CurrentAmount: number;
          EndDate: string;
          expense_details: Json | null;
          has_affiliate: boolean;
          has_collaborator: boolean;
          id: string;
          Notes: string | null;
          PayoutStatus: string | null;
          ProjectId: string;
          StartDate: string;
          Status: string;
          updated_at: string;
        };
        Insert: {
          ActualAmount: number;
          AffiliateCommission?: number | null;
          affiliateId?: string | null;
          ApprovalDate?: string | null;
          ApprovedBy?: string | null;
          Category: string;
          ClientId?: string | null;
          collaborators?: Json | null;
          created_at?: string;
          Currency: string;
          CurrentAmount: number;
          EndDate: string;
          expense_details?: Json | null;
          has_affiliate?: boolean;
          has_collaborator?: boolean;
          id?: string;
          Notes?: string | null;
          PayoutStatus?: string | null;
          ProjectId: string;
          StartDate: string;
          Status: string;
          updated_at?: string;
        };
        Update: {
          ActualAmount?: number;
          AffiliateCommission?: number | null;
          affiliateId?: string | null;
          ApprovalDate?: string | null;
          ApprovedBy?: string | null;
          Category?: string;
          ClientId?: string | null;
          collaborators?: Json | null;
          created_at?: string;
          Currency?: string;
          CurrentAmount?: number;
          EndDate?: string;
          expense_details?: Json | null;
          has_affiliate?: boolean;
          has_collaborator?: boolean;
          id?: string;
          Notes?: string | null;
          PayoutStatus?: string | null;
          ProjectId?: string;
          StartDate?: string;
          Status?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_budgets_affiliate';
            columns: ['affiliateId'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_budgets_client';
            columns: ['ClientId'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_budgets_project';
            columns: ['ProjectId'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      clients: {
        Row: {
          address: Json | null;
          company_name: string | null;
          contact_person: string | null;
          created_at: string;
          created_by: string | null;
          description: string | null;
          email: string | null;
          id: string;
          industry: string | null;
          is_active: boolean;
          name: string;
          phone: string | null;
          updated_at: string;
        };
        Insert: {
          address?: Json | null;
          company_name?: string | null;
          contact_person?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          email?: string | null;
          id?: string;
          industry?: string | null;
          is_active?: boolean;
          name: string;
          phone?: string | null;
          updated_at?: string;
        };
        Update: {
          address?: Json | null;
          company_name?: string | null;
          contact_person?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          email?: string | null;
          id?: string;
          industry?: string | null;
          is_active?: boolean;
          name?: string;
          phone?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      cycles: {
        Row: {
          created_at: string | null;
          end_date: string;
          id: string;
          name: string;
          number: number;
          progress: number | null;
          start_date: string;
          team_id: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          end_date: string;
          id: string;
          name: string;
          number: number;
          progress?: number | null;
          start_date: string;
          team_id?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          end_date?: string;
          id?: string;
          name?: string;
          number?: number;
          progress?: number | null;
          start_date?: string;
          team_id?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'cycles_team_id_fkey';
            columns: ['team_id'];
            isOneToOne: false;
            referencedRelation: 'teams';
            referencedColumns: ['id'];
          },
        ];
      };
      Emails: {
        Row: {
          created_at: string;
          email_address: string | null;
          id: number;
        };
        Insert: {
          created_at?: string;
          email_address?: string | null;
          id?: number;
        };
        Update: {
          created_at?: string;
          email_address?: string | null;
          id?: number;
        };
        Relationships: [];
      };
      health_status: {
        Row: {
          color: string;
          description: string | null;
          id: string;
          name: string;
          sort_order: number | null;
        };
        Insert: {
          color: string;
          description?: string | null;
          id: string;
          name: string;
          sort_order?: number | null;
        };
        Update: {
          color?: string;
          description?: string | null;
          id?: string;
          name?: string;
          sort_order?: number | null;
        };
        Relationships: [];
      };
      invoice_items: {
        Row: {
          amount: number;
          created_at: string;
          description: string;
          id: string;
          invoice_id: string;
          quantity: number;
          unit_price: number;
        };
        Insert: {
          amount: number;
          created_at?: string;
          description: string;
          id?: string;
          invoice_id: string;
          quantity?: number;
          unit_price: number;
        };
        Update: {
          amount?: number;
          created_at?: string;
          description?: string;
          id?: string;
          invoice_id?: string;
          quantity?: number;
          unit_price?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'invoice_items_invoice_id_fkey';
            columns: ['invoice_id'];
            isOneToOne: false;
            referencedRelation: 'invoices';
            referencedColumns: ['id'];
          },
        ];
      };
      invoices: {
        Row: {
          amount: number;
          created_at: string;
          created_by: string | null;
          due_date: string;
          id: string;
          invoice_number: string;
          is_archived: boolean;
          issue_date: string;
          notes: string | null;
          paid_date: string | null;
          project_id: string | null;
          status: Database['public']['Enums']['invoice_status'];
          tax_amount: number | null;
          total_amount: number;
          updated_at: string;
          user_id: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string;
          created_by?: string | null;
          due_date: string;
          id?: string;
          invoice_number: string;
          is_archived?: boolean;
          issue_date?: string;
          notes?: string | null;
          paid_date?: string | null;
          project_id?: string | null;
          status?: Database['public']['Enums']['invoice_status'];
          tax_amount?: number | null;
          total_amount: number;
          updated_at?: string;
          user_id?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string;
          created_by?: string | null;
          due_date?: string;
          id?: string;
          invoice_number?: string;
          is_archived?: boolean;
          issue_date?: string;
          notes?: string | null;
          paid_date?: string | null;
          project_id?: string | null;
          status?: Database['public']['Enums']['invoice_status'];
          tax_amount?: number | null;
          total_amount?: number;
          updated_at?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'invoices_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      issue_labels: {
        Row: {
          id: string;
          issue_id: string | null;
          label_id: string | null;
        };
        Insert: {
          id?: string;
          issue_id?: string | null;
          label_id?: string | null;
        };
        Update: {
          id?: string;
          issue_id?: string | null;
          label_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'issue_labels_issue_id_fkey';
            columns: ['issue_id'];
            isOneToOne: false;
            referencedRelation: 'issues';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issue_labels_label_id_fkey';
            columns: ['label_id'];
            isOneToOne: false;
            referencedRelation: 'labels';
            referencedColumns: ['id'];
          },
        ];
      };
      issues: {
        Row: {
          assignee_id: string | null;
          created_at: string | null;
          created_by: string;
          cycle_id: string | null;
          description: string | null;
          due_date: string | null;
          id: string;
          identifier: string;
          parent_issue_id: string | null;
          priority_id: string;
          project_id: string | null;
          rank: string;
          status_id: string;
          title: string;
          updated_at: string | null;
        };
        Insert: {
          assignee_id?: string | null;
          created_at?: string | null;
          created_by: string;
          cycle_id?: string | null;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          identifier: string;
          parent_issue_id?: string | null;
          priority_id: string;
          project_id?: string | null;
          rank: string;
          status_id: string;
          title: string;
          updated_at?: string | null;
        };
        Update: {
          assignee_id?: string | null;
          created_at?: string | null;
          created_by?: string;
          cycle_id?: string | null;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          identifier?: string;
          parent_issue_id?: string | null;
          priority_id?: string;
          project_id?: string | null;
          rank?: string;
          status_id?: string;
          title?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'issues_assignee_id_fkey';
            columns: ['assignee_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_created_by_fkey';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_cycle_id_fkey';
            columns: ['cycle_id'];
            isOneToOne: false;
            referencedRelation: 'cycles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_parent_issue_id_fkey';
            columns: ['parent_issue_id'];
            isOneToOne: false;
            referencedRelation: 'issues';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_priority_id_fkey';
            columns: ['priority_id'];
            isOneToOne: false;
            referencedRelation: 'priorities';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'issues_status_id_fkey';
            columns: ['status_id'];
            isOneToOne: false;
            referencedRelation: 'status';
            referencedColumns: ['id'];
          },
        ];
      };
      JOIN_US_TABLE: {
        Row: {
          additional_info: string | null;
          approved: Database['public']['Enums']['is_accepted'];
          areas: string[] | null;
          available_days: string[] | null;
          created_at: string;
          dob: string | null;
          email: string;
          equipment: string | null;
          full_name: string | null;
          hours_per_week: string | null;
          id: number;
          interests: string | null;
          is_nda_signed: boolean | null;
          is_vol_form_submited: boolean;
          join_role: Database['public']['Enums']['Role Types'];
          location: string | null;
          message: string | null;
          nda_signed_date: string | null;
          newsletter: boolean | null;
          other_area: string | null;
          past_experience: string | null;
          phone: string | null;
          position: string | null;
          position_other: string | null;
          preferred_time: string | null;
          referer: string | null;
          resume_url: string | null;
          reviewed: Database['public']['Enums']['is_reviewed'];
          skills: string | null;
          user_id: string;
          why_join: string | null;
        };
        Insert: {
          additional_info?: string | null;
          approved?: Database['public']['Enums']['is_accepted'];
          areas?: string[] | null;
          available_days?: string[] | null;
          created_at?: string;
          dob?: string | null;
          email: string;
          equipment?: string | null;
          full_name?: string | null;
          hours_per_week?: string | null;
          id?: number;
          interests?: string | null;
          is_nda_signed?: boolean | null;
          is_vol_form_submited?: boolean;
          join_role?: Database['public']['Enums']['Role Types'];
          location?: string | null;
          message?: string | null;
          nda_signed_date?: string | null;
          newsletter?: boolean | null;
          other_area?: string | null;
          past_experience?: string | null;
          phone?: string | null;
          position?: string | null;
          position_other?: string | null;
          preferred_time?: string | null;
          referer?: string | null;
          resume_url?: string | null;
          reviewed?: Database['public']['Enums']['is_reviewed'];
          skills?: string | null;
          user_id?: string;
          why_join?: string | null;
        };
        Update: {
          additional_info?: string | null;
          approved?: Database['public']['Enums']['is_accepted'];
          areas?: string[] | null;
          available_days?: string[] | null;
          created_at?: string;
          dob?: string | null;
          email?: string;
          equipment?: string | null;
          full_name?: string | null;
          hours_per_week?: string | null;
          id?: number;
          interests?: string | null;
          is_nda_signed?: boolean | null;
          is_vol_form_submited?: boolean;
          join_role?: Database['public']['Enums']['Role Types'];
          location?: string | null;
          message?: string | null;
          nda_signed_date?: string | null;
          newsletter?: boolean | null;
          other_area?: string | null;
          past_experience?: string | null;
          phone?: string | null;
          position?: string | null;
          position_other?: string | null;
          preferred_time?: string | null;
          referer?: string | null;
          resume_url?: string | null;
          reviewed?: Database['public']['Enums']['is_reviewed'];
          skills?: string | null;
          user_id?: string;
          why_join?: string | null;
        };
        Relationships: [];
      };
      labels: {
        Row: {
          color: string;
          created_at: string | null;
          id: string;
          name: string;
        };
        Insert: {
          color: string;
          created_at?: string | null;
          id: string;
          name: string;
        };
        Update: {
          color?: string;
          created_at?: string | null;
          id?: string;
          name?: string;
        };
        Relationships: [];
      };
      message_recipients: {
        Row: {
          created_at: string;
          id: string;
          is_read: boolean;
          message_id: string;
          recipient_id: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          is_read?: boolean;
          message_id: string;
          recipient_id: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          is_read?: boolean;
          message_id?: string;
          recipient_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'message_recipients_message_id_fkey';
            columns: ['message_id'];
            isOneToOne: false;
            referencedRelation: 'messages';
            referencedColumns: ['id'];
          },
        ];
      };
      messages: {
        Row: {
          content: string;
          created_at: string;
          id: string;
          is_archived: boolean;
          is_starred: boolean;
          sender_id: string;
          subject: string;
          updated_at: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          id?: string;
          is_archived?: boolean;
          is_starred?: boolean;
          sender_id: string;
          subject: string;
          updated_at?: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          id?: string;
          is_archived?: boolean;
          is_starred?: boolean;
          sender_id?: string;
          subject?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      payments: {
        Row: {
          amount: number;
          created_at: string;
          id: string;
          invoice_id: string | null;
          notes: string | null;
          payment_date: string;
          payment_method: Database['public']['Enums']['payment_method'];
          transaction_id: string | null;
          updated_at: string;
          user_id: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string;
          id?: string;
          invoice_id?: string | null;
          notes?: string | null;
          payment_date?: string;
          payment_method: Database['public']['Enums']['payment_method'];
          transaction_id?: string | null;
          updated_at?: string;
          user_id?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string;
          id?: string;
          invoice_id?: string | null;
          notes?: string | null;
          payment_date?: string;
          payment_method?: Database['public']['Enums']['payment_method'];
          transaction_id?: string | null;
          updated_at?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'payments_invoice_id_fkey';
            columns: ['invoice_id'];
            isOneToOne: false;
            referencedRelation: 'invoices';
            referencedColumns: ['id'];
          },
        ];
      };
      priorities: {
        Row: {
          created_at: string | null;
          icon_name: string | null;
          id: string;
          name: string;
          sort_order: number | null;
        };
        Insert: {
          created_at?: string | null;
          icon_name?: string | null;
          id: string;
          name: string;
          sort_order?: number | null;
        };
        Update: {
          created_at?: string | null;
          icon_name?: string | null;
          id?: string;
          name?: string;
          sort_order?: number | null;
        };
        Relationships: [];
      };
      profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string | null;
          email: string;
          full_name: string | null;
          id: string;
          joined_date: string | null;
          name: string;
          role: Database['public']['Enums']['Role Types'];
          status: string | null;
          updated_at: string | null;
          user_id: string | null;
          username: string | null;
          users_id: number | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string | null;
          email: string;
          full_name?: string | null;
          id: string;
          joined_date?: string | null;
          name: string;
          role?: Database['public']['Enums']['Role Types'];
          status?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          username?: string | null;
          users_id?: number | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string | null;
          email?: string;
          full_name?: string | null;
          id?: string;
          joined_date?: string | null;
          name?: string;
          role?: Database['public']['Enums']['Role Types'];
          status?: string | null;
          updated_at?: string | null;
          user_id?: string | null;
          username?: string | null;
          users_id?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_users_id_fkey';
            columns: ['users_id'];
            isOneToOne: false;
            referencedRelation: 'JOIN_US_TABLE';
            referencedColumns: ['id'];
          },
        ];
      };
      project_members: {
        Row: {
          id: string;
          joined_at: string;
          project_id: string;
          role: string;
          user_id: string;
        };
        Insert: {
          id?: string;
          joined_at?: string;
          project_id: string;
          role: string;
          user_id: string;
        };
        Update: {
          id?: string;
          joined_at?: string;
          project_id?: string;
          role?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_members_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_members_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      project_tasks: {
        Row: {
          assigned_to: string | null;
          created_at: string;
          created_by: string;
          description: string | null;
          due_date: string | null;
          id: string;
          priority: string;
          project_id: string;
          status: string;
          title: string;
          updated_at: string;
        };
        Insert: {
          assigned_to?: string | null;
          created_at?: string;
          created_by: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority: string;
          project_id: string;
          status: string;
          title: string;
          updated_at?: string;
        };
        Update: {
          assigned_to?: string | null;
          created_at?: string;
          created_by?: string;
          description?: string | null;
          due_date?: string | null;
          id?: string;
          priority?: string;
          project_id?: string;
          status?: string;
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'project_tasks_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      projects: {
        Row: {
          affiliate_user: string | null;
          budget: number | null;
          budget_id: string | null;
          client_id: string | null;
          completed_date: string | null;
          created_at: string;
          created_by: string | null;
          description: string | null;
          due_date: string | null;
          health_id: string | null;
          icon: string | null;
          id: string;
          is_archived: boolean;
          is_proposed: boolean | null;
          lead_id: string | null;
          members: string[] | null;
          name: string;
          percent_complete: number | null;
          priority: Database['public']['Enums']['project_priority'];
          priority_id: string | null;
          start_date: string | null;
          status: Database['public']['Enums']['project_status'];
          status_id: string | null;
          target_date: string | null;
          updated_at: string;
        };
        Insert: {
          affiliate_user?: string | null;
          budget?: number | null;
          budget_id?: string | null;
          client_id?: string | null;
          completed_date?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          due_date?: string | null;
          health_id?: string | null;
          icon?: string | null;
          id?: string;
          is_archived?: boolean;
          is_proposed?: boolean | null;
          lead_id?: string | null;
          members?: string[] | null;
          name: string;
          percent_complete?: number | null;
          priority?: Database['public']['Enums']['project_priority'];
          priority_id?: string | null;
          start_date?: string | null;
          status?: Database['public']['Enums']['project_status'];
          status_id?: string | null;
          target_date?: string | null;
          updated_at?: string;
        };
        Update: {
          affiliate_user?: string | null;
          budget?: number | null;
          budget_id?: string | null;
          client_id?: string | null;
          completed_date?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          due_date?: string | null;
          health_id?: string | null;
          icon?: string | null;
          id?: string;
          is_archived?: boolean;
          is_proposed?: boolean | null;
          lead_id?: string | null;
          members?: string[] | null;
          name?: string;
          percent_complete?: number | null;
          priority?: Database['public']['Enums']['project_priority'];
          priority_id?: string | null;
          start_date?: string | null;
          status?: Database['public']['Enums']['project_status'];
          status_id?: string | null;
          target_date?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_projects_budget';
            columns: ['budget_id'];
            isOneToOne: false;
            referencedRelation: 'budgets';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_projects_health';
            columns: ['health_id'];
            isOneToOne: false;
            referencedRelation: 'health_status';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_projects_priority';
            columns: ['priority_id'];
            isOneToOne: false;
            referencedRelation: 'priorities';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_projects_status';
            columns: ['status_id'];
            isOneToOne: false;
            referencedRelation: 'status';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'projects_affiliate_user_fkey';
            columns: ['affiliate_user'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'projects_client_id_fkey';
            columns: ['client_id'];
            isOneToOne: false;
            referencedRelation: 'clients';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'projects_lead_id_fkey';
            columns: ['lead_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      status: {
        Row: {
          color: string;
          created_at: string | null;
          icon_name: string | null;
          id: string;
          name: string;
          sort_order: number | null;
        };
        Insert: {
          color: string;
          created_at?: string | null;
          icon_name?: string | null;
          id: string;
          name: string;
          sort_order?: number | null;
        };
        Update: {
          color?: string;
          created_at?: string | null;
          icon_name?: string | null;
          id?: string;
          name?: string;
          sort_order?: number | null;
        };
        Relationships: [];
      };
      task_comments: {
        Row: {
          content: string;
          created_at: string;
          id: string;
          task_id: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          content: string;
          created_at?: string;
          id?: string;
          task_id: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          content?: string;
          created_at?: string;
          id?: string;
          task_id?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'task_comments_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'tasks';
            referencedColumns: ['id'];
          },
        ];
      };
      tasks: {
        Row: {
          actual_hours: number | null;
          assigned_to: string | null;
          created_at: string;
          created_by: string | null;
          description: string | null;
          due_date: string | null;
          estimated_hours: number | null;
          id: string;
          is_archived: boolean;
          priority: Database['public']['Enums']['task_priority'];
          project_id: string;
          status: Database['public']['Enums']['task_status'];
          title: string;
          updated_at: string;
        };
        Insert: {
          actual_hours?: number | null;
          assigned_to?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          due_date?: string | null;
          estimated_hours?: number | null;
          id?: string;
          is_archived?: boolean;
          priority?: Database['public']['Enums']['task_priority'];
          project_id: string;
          status?: Database['public']['Enums']['task_status'];
          title: string;
          updated_at?: string;
        };
        Update: {
          actual_hours?: number | null;
          assigned_to?: string | null;
          created_at?: string;
          created_by?: string | null;
          description?: string | null;
          due_date?: string | null;
          estimated_hours?: number | null;
          id?: string;
          is_archived?: boolean;
          priority?: Database['public']['Enums']['task_priority'];
          project_id?: string;
          status?: Database['public']['Enums']['task_status'];
          title?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tasks_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      team_members: {
        Row: {
          id: string;
          joined: boolean | null;
          joined_at: string | null;
          team_id: string | null;
          user_id: string | null;
        };
        Insert: {
          id?: string;
          joined?: boolean | null;
          joined_at?: string | null;
          team_id?: string | null;
          user_id?: string | null;
        };
        Update: {
          id?: string;
          joined?: boolean | null;
          joined_at?: string | null;
          team_id?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'team_members_team_id_fkey';
            columns: ['team_id'];
            isOneToOne: false;
            referencedRelation: 'teams';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'team_members_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      teams: {
        Row: {
          color: string | null;
          created_at: string | null;
          description: string | null;
          icon: string | null;
          id: string;
          members: string[] | null;
          name: string;
          projects: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id: string;
          members?: string[] | null;
          name: string;
          projects?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          color?: string | null;
          created_at?: string | null;
          description?: string | null;
          icon?: string | null;
          id?: string;
          members?: string[] | null;
          name?: string;
          projects?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      time_entries: {
        Row: {
          created_at: string;
          description: string | null;
          duration_minutes: number | null;
          end_time: string | null;
          id: string;
          start_time: string;
          task_id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          duration_minutes?: number | null;
          end_time?: string | null;
          id?: string;
          start_time: string;
          task_id: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          duration_minutes?: number | null;
          end_time?: string | null;
          id?: string;
          start_time?: string;
          task_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'time_entries_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'tasks';
            referencedColumns: ['id'];
          },
        ];
      };
      transactions: {
        Row: {
          amount: number;
          category: string;
          created_at: string;
          created_by: string;
          date: string;
          description: string;
          id: string;
          notes: string | null;
          type: string;
          updated_at: string;
        };
        Insert: {
          amount: number;
          category: string;
          created_at?: string;
          created_by: string;
          date: string;
          description: string;
          id?: string;
          notes?: string | null;
          type: string;
          updated_at?: string;
        };
        Update: {
          amount?: number;
          category?: string;
          created_at?: string;
          created_by?: string;
          date?: string;
          description?: string;
          id?: string;
          notes?: string | null;
          type?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      waitlists: {
        Row: {
          created_at: string;
          email_address: string;
          full_name: string | null;
          id: number;
          is_ld_email_sent: boolean | null;
        };
        Insert: {
          created_at?: string;
          email_address: string;
          full_name?: string | null;
          id?: number;
          is_ld_email_sent?: boolean | null;
        };
        Update: {
          created_at?: string;
          email_address?: string;
          full_name?: string | null;
          id?: number;
          is_ld_email_sent?: boolean | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      exec_sql: {
        Args: { sql: string };
        Returns: undefined;
      };
      user_has_role: {
        Args: { user_id: string; required_role: string };
        Returns: boolean;
      };
    };
    Enums: {
      invoice_status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
      is_accepted: 'accepted' | 'reviewing' | 'notAccepted';
      is_reviewed: 'reviewed' | 'received' | 'notAccepted';
      payment_method:
        | 'bank_transfer'
        | 'credit_card'
        | 'paypal'
        | 'stripe'
        | 'other';
      project_priority: 'low' | 'medium' | 'high' | 'urgent';
      project_status:
        | 'planning'
        | 'in_progress'
        | 'on_hold'
        | 'completed'
        | 'cancelled';
      'Role Types': 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer';
      task_priority: 'low' | 'medium' | 'high' | 'urgent';
      task_status: 'backlog' | 'todo' | 'in_progress' | 'review' | 'completed';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      invoice_status: ['draft', 'sent', 'paid', 'overdue', 'cancelled'],
      is_accepted: ['accepted', 'reviewing', 'notAccepted'],
      is_reviewed: ['reviewed', 'received', 'notAccepted'],
      payment_method: [
        'bank_transfer',
        'credit_card',
        'paypal',
        'stripe',
        'other',
      ],
      project_priority: ['low', 'medium', 'high', 'urgent'],
      project_status: [
        'planning',
        'in_progress',
        'on_hold',
        'completed',
        'cancelled',
      ],
      'Role Types': ['Admin', 'Collaborator', 'Affiliate', 'Volunteer'],
      task_priority: ['low', 'medium', 'high', 'urgent'],
      task_status: ['backlog', 'todo', 'in_progress', 'review', 'completed'],
    },
  },
} as const;
