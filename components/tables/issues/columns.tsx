'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

import { AssigneeUser } from '@/components/issues/assignee-user';
import { LabelBadge } from '@/components/issues/label-badge';
import { PrioritySelector } from '@/components/issues/priority-selector';
import { ProjectBadge } from '@/components/issues/project-badge';
import { StatusSelector } from '@/components/issues/status-selector';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useIssues } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewIssue?: (issue: Issue) => void;
}

export const createColumns = ({
  onViewIssue,
}: ColumnsProps = {}): ColumnDef<Issue>[] => {
  return [
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }) => {
        const issue = row.original;
        return (
          <div className='flex items-center gap-2'>
            <div className='flex flex-col items-start overflow-hidden'>
              <Link
                href={`/admin/issues/${issue.id}`}
                className='font-medium truncate w-full hover:underline'
              >
                {issue.title}
              </Link>
              {issue.description && (
                <p className='text-xs text-muted-foreground truncate w-full'>
                  {issue.description}
                </p>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handleStatusChange = (statusId: string) => {
          const updatePromise = updateIssue(issue.id, { status_id: statusId });

          toast.promise(updatePromise, {
            loading: 'Updating issue status...',
            success: 'Issue status updated successfully',
            error: (error) =>
              `Failed to update status: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <StatusSelector
            status={issue.status || null}
            issueId={issue.id}
            onChange={(status) => {
              if (status) {
                handleStatusChange(status.id);
              }
            }}
          />
        );
      },
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handlePriorityChange = (priorityId: string) => {
          const updatePromise = updateIssue(issue.id, {
            priority_id: priorityId,
          });

          toast.promise(updatePromise, {
            loading: 'Updating issue priority...',
            success: 'Issue priority updated successfully',
            error: (error) =>
              `Failed to update priority: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <PrioritySelector
            priority={issue.priority || null}
            issueId={issue.id}
            onChange={(priority) => {
              if (priority) {
                handlePriorityChange(priority.id);
              }
            }}
          />
        );
      },
    },
    {
      accessorKey: 'assignee',
      header: 'Assignee',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handleAssigneeChange = (assigneeId: string | null) => {
          const updatePromise = updateIssue(issue.id, {
            assignee_id: assigneeId,
          });

          const assigneeText = assigneeId ? 'assigned' : 'unassigned';

          toast.promise(updatePromise, {
            loading: 'Updating issue assignee...',
            success: `Issue ${assigneeText} successfully`,
            error: (error) =>
              `Failed to update assignee: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <AssigneeUser
            user={
              issue.assignee
                ? {
                    id: issue.assignee.id,
                    name: issue.assignee.name || issue.assignee.email,
                    email: issue.assignee.email,
                    avatar_url: issue.assignee.avatar_url,
                    status: issue.assignee.status,
                  }
                : null
            }
            issueId={issue.id}
            onChange={(user) => {
              handleAssigneeChange(user?.id || null);
            }}
          />
        );
      },
    },
    {
      accessorKey: 'project',
      header: 'Project',
      cell: ({ row }) => {
        const issue = row.original;
        return issue.project ? (
          <ProjectBadge
            project={{
              id: issue.project.id,
              name: issue.project.name,
              description: issue.project.description,
              icon: issue.project.icon,
            }}
          />
        ) : (
          <span className='text-muted-foreground text-sm'>No project</span>
        );
      },
    },
    {
      accessorKey: 'labels',
      header: 'Labels',
      cell: ({ row }) => {
        const issue = row.original;
        if (!issue.labels || issue.labels.length === 0) {
          return (
            <span className='text-muted-foreground text-sm'>No labels</span>
          );
        }

        return (
          <div className='flex flex-wrap gap-1'>
            <LabelBadge labels={issue.labels.slice(0, 2)} />
            {issue.labels.length > 2 && (
              <span className='text-xs text-muted-foreground'>
                +{issue.labels.length - 2} more
              </span>
            )}
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const issue = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem asChild>
                <Link href={`/admin/issues/${issue.id}`}>
                  <Eye className='mr-2 h-4 w-4' />
                  View Issue
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
};
