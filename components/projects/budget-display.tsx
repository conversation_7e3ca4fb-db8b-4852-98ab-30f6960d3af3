'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import type { Budget } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/format-currency';

interface BudgetDisplayProps {
  budget: Budget | null;
  className?: string;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'locked':
      return 'bg-yellow-100 text-yellow-800';
    case 'spent':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'marketing':
      return 'bg-blue-100 text-blue-800';
    case 'development':
      return 'bg-purple-100 text-purple-800';
    case 'consulting':
      return 'bg-orange-100 text-orange-800';
    case 'operations':
      return 'bg-green-100 text-green-800';
    case 'other':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function BudgetDisplay({ budget, className }: BudgetDisplayProps) {
  if (!budget) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className='text-lg'>Budget</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>No budget assigned</p>
        </CardContent>
      </Card>
    );
  }

  const spentAmount = budget.ActualAmount - budget.CurrentAmount;
  const spentPercentage = (spentAmount / budget.ActualAmount) * 100;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='text-lg'>Budget</CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <span className='text-sm font-medium'>Total Budget</span>
          <span className='text-lg font-bold'>
            {formatCurrency(budget.ActualAmount, budget.Currency)}
          </span>
        </div>

        <div className='flex justify-between items-center'>
          <span className='text-sm font-medium'>Remaining</span>
          <span className='text-lg font-bold'>
            {formatCurrency(budget.CurrentAmount, budget.Currency)}
          </span>
        </div>

        <div className='space-y-2'>
          <div className='flex justify-between text-sm'>
            <span>Spent</span>
            <span>{spentPercentage.toFixed(1)}%</span>
          </div>
          <Progress value={spentPercentage} className='h-2' />
        </div>

        <div className='flex gap-2 flex-wrap'>
          <Badge className={getStatusColor(budget.Status)}>
            {budget.Status}
          </Badge>
          <Badge className={getCategoryColor(budget.Category)}>
            {budget.Category}
          </Badge>
        </div>

        {budget.has_affiliate && budget.AffiliateCommission && (
          <div className='pt-2 border-t'>
            <div className='flex justify-between items-center'>
              <span className='text-sm font-medium'>Affiliate Commission</span>
              <span className='text-sm'>
                {formatCurrency(budget.AffiliateCommission, budget.Currency)}
              </span>
            </div>
          </div>
        )}

        {budget.Notes && (
          <div className='pt-2 border-t'>
            <p className='text-sm text-muted-foreground'>{budget.Notes}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
