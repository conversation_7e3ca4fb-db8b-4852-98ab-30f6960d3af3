'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Eye, ExternalLink, MoreHorizontal } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Application } from '@/lib/supabase/database-modules';

import { ApprovedStatusSelector } from '@/components/applications/approved-status-selector';
import { ReviewedStatusSelector } from '@/components/applications/reviewed-status-selector';

interface ColumnsProps {
  onViewApplication: (application: Application) => void;
  onReviewedChange: (
    applicationId: number,
    reviewed: 'reviewed' | 'received' | 'notAccepted'
  ) => void;
  onApprovedChange: (
    applicationId: number,
    approved: 'accepted' | 'reviewing' | 'notAccepted'
  ) => void;
}

export const createColumns = ({
  onViewApplication,
  onReviewedChange,
  onApprovedChange,
}: ColumnsProps): ColumnDef<Application>[] => [
  {
    accessorKey: 'full_name',
    header: 'Full Name',
    cell: ({ row }) => {
      const fullName = row.getValue('full_name') as string;
      return (
        <div className='font-medium'>{fullName || 'No name provided'}</div>
      );
    },
  },
  // {
  //   accessorKey: "phone",
  //   header: "Phone Number",
  //   cell: ({ row }) => {
  //     const phone = row.getValue("phone") as string
  //     return <div>{phone || "No phone provided"}</div>
  //   },
  // },
  // {
  //   accessorKey: "email",
  //   header: "Email",
  //   cell: ({ row }) => {
  //     const email = row.getValue("email") as string
  //     return <div>{email}</div>
  //   },
  // },
  {
    accessorKey: 'position',
    header: 'Position',
    cell: ({ row }) => {
      const position = row.getValue('position') as string;
      const positionOther = row.original.position_other;

      if (position === 'Other' && positionOther) {
        return <div>{positionOther}</div>;
      }
      return <div>{position || 'Not specified'}</div>;
    },
  },
  // {
  //   accessorKey: "resume_url",
  //   header: "Resume/CV",
  //   cell: ({ row }) => {
  //     const resumeUrl = row.getValue("resume_url") as string

  //     if (!resumeUrl) {
  //       return <span className="text-muted-foreground text-sm">No resume</span>
  //     }

  //     return (
  //       <Button
  //         variant="ghost"
  //         size="sm"
  //         className="h-8 px-2"
  //         onClick={() => window.open(resumeUrl, "_blank")}
  //       >
  //         <ExternalLink className="h-3 w-3 mr-1" />
  //         View
  //       </Button>
  //     )
  //   },
  // },
  {
    accessorKey: 'join_role',
    header: 'Role',
    cell: ({ row }) => {
      const role = row.getValue('join_role') as string;

      const getRoleBadgeVariant = (role: string) => {
        switch (role) {
          case 'Admin':
            return 'destructive';
          case 'Collaborator':
            return 'default';
          case 'Affiliate':
            return 'secondary';
          case 'Volunteer':
            return 'outline';
          default:
            return 'secondary';
        }
      };

      return <Badge variant={getRoleBadgeVariant(role)}>{role}</Badge>;
    },
  },
  {
    accessorKey: 'reviewed',
    header: 'Reviewed',
    cell: ({ row }) => {
      return (
        <ReviewedStatusSelector
          application={row.original}
          onReviewedChange={onReviewedChange}
        />
      );
    },
  },
  {
    accessorKey: 'approved',
    header: 'Approved',
    cell: ({ row }) => {
      return (
        <ApprovedStatusSelector
          application={row.original}
          onApprovedChange={onApprovedChange}
        />
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const application = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewApplication(application)}>
              <Eye className='mr-2 h-4 w-4' />
              View Application
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
