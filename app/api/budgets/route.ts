import { NextRequest, NextResponse } from 'next/server';
import { supabaseClient } from '@/lib/supabase/auth/client';
import { createClient } from '@supabase/supabase-js';
import type { CreateBudgetInput, Budget } from '@/lib/supabase/database-modules';

// Initialize Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/budgets
 * Fetch all budgets with optional filtering
 * Query parameters:
 * - project_id: Filter by project ID
 * - client_id: Filter by client ID
 * - status: Filter by budget status
 * - category: Filter by budget category
 * - affiliate_id: Filter by affiliate ID
 * - has_affiliate: Filter by affiliate presence (true/false)
 * - limit: Number of results to return (default: 50)
 * - offset: Number of results to skip (default: 0)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Extract query parameters
    const projectId = searchParams.get('project_id');
    const clientId = searchParams.get('client_id');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const affiliateId = searchParams.get('affiliate_id');
    const hasAffiliate = searchParams.get('has_affiliate');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = supabaseAdmin
      .from('budgets')
      .select(`
        *,
        project:projects!ProjectId(
          id,
          name,
          description,
          icon,
          percent_complete,
          start_date,
          target_date,
          lead_id,
          status_id,
          priority_id,
          health_id,
          created_at,
          updated_at,
          created_by,
          is_archived,
          members,
          is_proposed,
          affiliate_user,
          client_id,
          budget_id
        ),
        client:clients!ClientId(
          id,
          name,
          email,
          phone,
          description,
          company_name,
          industry,
          contact_person,
          address,
          created_at,
          updated_at,
          created_by,
          is_active
        ),
        affiliate:profiles!affiliateId(
          id,
          full_name,
          email,
          avatar_url,
          role,
          status,
          joined_date,
          created_at,
          updated_at
        ),
        approved_by_user:profiles!ApprovedBy(
          id,
          full_name,
          email,
          avatar_url,
          role,
          status,
          joined_date,
          created_at,
          updated_at
        )
      `);

    // Apply filters
    if (projectId) {
      query = query.eq('ProjectId', projectId);
    }
    if (clientId) {
      query = query.eq('ClientId', clientId);
    }
    if (status) {
      query = query.eq('Status', status);
    }
    if (category) {
      query = query.eq('Category', category);
    }
    if (affiliateId) {
      query = query.eq('affiliateId', affiliateId);
    }
    if (hasAffiliate !== null) {
      query = query.eq('has_affiliate', hasAffiliate === 'true');
    }

    // Apply pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: budgets, error, count } = await query;

    if (error) {
      console.error('Error fetching budgets:', error);
      return NextResponse.json(
        { error: 'Failed to fetch budgets', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: budgets,
      count,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error('Unexpected error in GET /api/budgets:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/budgets
 * Create a new budget
 */
export async function POST(request: NextRequest) {
  try {
    const budgetInput: CreateBudgetInput = await request.json();

    // Validate required fields
    if (!budgetInput.ProjectId || !budgetInput.ActualAmount || !budgetInput.StartDate || !budgetInput.EndDate) {
      return NextResponse.json(
        { error: 'Missing required fields: ProjectId, ActualAmount, StartDate, EndDate' },
        { status: 400 }
      );
    }

    // Validate amount is positive
    if (budgetInput.ActualAmount <= 0 || budgetInput.CurrentAmount < 0) {
      return NextResponse.json(
        { error: 'ActualAmount must be positive and CurrentAmount must be non-negative' },
        { status: 400 }
      );
    }

    // Validate dates
    const startDate = new Date(budgetInput.StartDate);
    const endDate = new Date(budgetInput.EndDate);
    if (endDate <= startDate) {
      return NextResponse.json(
        { error: 'EndDate must be after StartDate' },
        { status: 400 }
      );
    }

    // Set default values
    const budgetData = {
      ...budgetInput,
      Currency: budgetInput.Currency || 'USD',
      Category: budgetInput.Category || 'development',
      Status: budgetInput.Status || 'draft',
      has_affiliate: budgetInput.has_affiliate || false,
      has_collaborator: budgetInput.has_collaborator || false,
      CurrentAmount: budgetInput.CurrentAmount ?? budgetInput.ActualAmount,
    };

    // Validate affiliate commission if has_affiliate is true
    if (budgetData.has_affiliate && !budgetData.AffiliateCommission) {
      return NextResponse.json(
        { error: 'AffiliateCommission is required when has_affiliate is true' },
        { status: 400 }
      );
    }

    const { data: budget, error } = await supabaseAdmin
      .from('budgets')
      .insert([budgetData])
      .select(`
        *,
        project:projects!ProjectId(
          id,
          name,
          description,
          icon,
          percent_complete,
          start_date,
          target_date,
          lead_id,
          status_id,
          priority_id,
          health_id,
          created_at,
          updated_at,
          created_by,
          is_archived,
          members,
          is_proposed,
          affiliate_user,
          client_id,
          budget_id
        ),
        client:clients!ClientId(
          id,
          name,
          email,
          phone,
          description,
          company_name,
          industry,
          contact_person,
          address,
          created_at,
          updated_at,
          created_by,
          is_active
        ),
        affiliate:profiles!affiliateId(
          id,
          full_name,
          email,
          avatar_url,
          role,
          status,
          joined_date,
          created_at,
          updated_at
        ),
        approved_by_user:profiles!ApprovedBy(
          id,
          full_name,
          email,
          avatar_url,
          role,
          status,
          joined_date,
          created_at,
          updated_at
        )
      `)
      .single();

    if (error) {
      console.error('Error creating budget:', error);
      return NextResponse.json(
        { error: 'Failed to create budget', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: budget }, { status: 201 });

  } catch (error) {
    console.error('Unexpected error in POST /api/budgets:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
