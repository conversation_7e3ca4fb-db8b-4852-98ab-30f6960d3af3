'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Shield, AlertTriangle } from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { usePortfolio } from '@/hooks/use-db';

interface NDAGuardProps {
  children: React.ReactNode;
}

export function NDAGuard({ children }: NDAGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { portfolioData, loading, error } = usePortfolio();

  // Allow access to the NDA page itself
  const isNDAPage = pathname === '/affiliate/nda';

  useEffect(() => {
    // Debug logging for NDA Guard (development only)
    if (process.env.NODE_ENV === 'development') {
      console.log('NDA Guard check:', {
        loading,
        isNDAPage,
        error: !!error,
        hasPortfolioData: !!portfolioData,
        is_nda_signed: portfolioData?.is_nda_signed,
        pathname,
      });
    }

    // Don't redirect if we're loading or on the NDA page
    if (loading || isNDAPage) return;

    // If there's an error loading portfolio data, redirect to NDA page
    if (error) {
      if (process.env.NODE_ENV === 'development') {
        console.log('NDA Guard redirecting due to error:', error);
      }
      router.push('/affiliate/nda');
      return;
    }

    // If portfolio data is loaded and NDA is not signed, redirect to NDA page
    if (portfolioData && !portfolioData.is_nda_signed) {
      if (process.env.NODE_ENV === 'development') {
        console.log(
          'NDA Guard redirecting - NDA not signed:',
          portfolioData.is_nda_signed
        );
      }
      router.push('/affiliate/nda');
      return;
    }

    if (
      portfolioData?.is_nda_signed &&
      process.env.NODE_ENV === 'development'
    ) {
      console.log('NDA Guard allowing access - NDA is signed');
    }
  }, [portfolioData, loading, error, isNDAPage, router, pathname]);

  // Show loading state
  if (loading) {
    return (
      <div className='h-full flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className='h-full flex items-center justify-center p-6'>
        <Card className='max-w-md w-full'>
          <CardHeader className='text-center'>
            <div className='mx-auto w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4'>
              <AlertTriangle className='w-6 h-6 text-red-600 dark:text-red-400' />
            </div>
            <CardTitle>Access Error</CardTitle>
            <CardDescription>
              Unable to verify your access permissions
            </CardDescription>
          </CardHeader>
          <CardContent className='text-center'>
            <p className='text-sm text-muted-foreground mb-4'>{error}</p>
            <Button
              onClick={() => router.push('/affiliate/nda')}
              className='w-full'
            >
              Continue to NDA
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If on NDA page, always show content
  if (isNDAPage) {
    return <>{children}</>;
  }

  // If NDA is not signed, show access denied (this should not happen due to redirect)
  if (portfolioData && !portfolioData.is_nda_signed) {
    return (
      <div className='h-full flex items-center justify-center p-6'>
        <Card className='max-w-md w-full'>
          <CardHeader className='text-center'>
            <div className='mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-4'>
              <Shield className='w-6 h-6 text-yellow-600 dark:text-yellow-400' />
            </div>
            <CardTitle>NDA Required</CardTitle>
            <CardDescription>
              You must sign the Non-Disclosure Agreement to access this area
            </CardDescription>
          </CardHeader>
          <CardContent className='text-center'>
            <p className='text-sm text-muted-foreground mb-4'>
              Please read and sign the NDA to continue using the affiliate
              dashboard.
            </p>
            <Button
              onClick={() => router.push('/affiliate/nda')}
              className='w-full'
            >
              Sign NDA
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If NDA is signed, show the protected content
  return <>{children}</>;
}
