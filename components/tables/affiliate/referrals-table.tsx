'use client';

import { useRouter } from 'next/navigation';

import { DataTable } from '@/components/ui/data-table';
import { useProposals } from '@/hooks/use-db';
import type { Proposal } from '@/lib/supabase/database-modules';

import { createReferralsColumns } from './referrals-columns';

export function ReferralsTable() {
  const router = useRouter();
  const { proposals, loading, error } = useProposals(true); // Use affiliate filtering

  const handleViewReferral = (proposal: Proposal) => {
    router.push(`/affiliate/referrals/${proposal.id}`);
  };

  const handleEditReferral = (proposal: Proposal) => {
    router.push(`/affiliate/referrals/${proposal.id}/edit`);
  };

  const columns = createReferralsColumns({
    onViewReferral: handleViewReferral,
    onEditReferral: handleEditReferral,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-32'>
        <div className='text-center'>
          <p className='text-destructive font-medium'>
            Error loading referrals
          </p>
          <p className='text-sm text-muted-foreground mt-1'>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <DataTable columns={columns} data={proposals} isLoading={loading} />
    </div>
  );
}
