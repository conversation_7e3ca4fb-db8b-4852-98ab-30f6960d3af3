import { FolderIcon } from 'lucide-react';
import Link from 'next/link';

import { Badge } from '@/components/ui/badge';

interface ProjectOption {
  id: string;
  name: string;
  description?: string | null;
  icon?: string | null;
}

interface ProjectBadgeProps {
  project: ProjectOption;
}

export function ProjectBadge({ project }: ProjectBadgeProps) {
  return (
    <Link
      href={`/admin/projects/${project.id}`}
      className='flex items-center justify-center gap-.5'
    >
      <Badge
        variant='outline'
        className='gap-1.5 rounded-full text-muted-foreground bg-background'
      >
        <FolderIcon size={16} />
        {project.name}
      </Badge>
    </Link>
  );
}
