'use client';

import type { AuthChangeEvent, Session } from '@supabase/supabase-js';
import { useCallback, useEffect, useState } from 'react';

import { notificationService } from '@/lib/services/notification-service';
import { userStore } from '@/lib/store/user';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type {
  Application,
  Budget,
  Client,
  CreateApplicationInput,
  CreateBudgetInput,
  CreateClientInput,
  CreateCycleInput,
  CreateIssueInput,
  CreateMemberInput,
  CreateProjectInput,
  CreateProjectMemberInput,
  CreateProposalInput,
  CreateTaskInput,
  CreateTeamInput,
  CreateTeamMemberInput,
  Cycle,
  FilterOptions,
  Issue,
  IssuesState,
  JoinUsTable_Types,
  Label,
  Member,
  PortfolioData,
  Priority,
  Profile_Types,
  Project,
  ProjectMember,
  ProjectMemberQueryResult,
  ProjectMemberWithRelations,
  Proposal,
  Status,
  Task,
  Team,
  TeamMember,
  UpdateBudgetInput,
  UpdateClientInput,
  UpdateMemberInput,
  UpdateProjectInput,
} from '@/lib/supabase/database-modules';
import {
  createStandardError,
  ERROR_MESSAGES,
  logError,
} from '@/lib/utils/error-utils';

const supabase = supabaseClient;

export function useProfile() {
  const {
    profile,
    user,
    removeProfile,
    removeUser,
    updateProfile,
    updateUser,
  } = userStore();

  const [userId, setUserId] = useState<string>(user?.id ?? '');

  // Memoize the session fetch and update logic
  const fetchSessionAndUpdateUser = useCallback(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        updateUser(session.user);
        setUserId(session.user.id);
      }
    });
  }, [updateUser]);

  // Memoize the auth state change handler
  const handleAuthStateChange = useCallback(
    (_event: AuthChangeEvent, session: Session | null) => {
      if (session) {
        updateUser(session.user);
        setUserId(session.user.id); // Optionally update userId here as well
      } else {
        removeUser(null);
        removeProfile(null);
      }
    },
    [updateUser, removeUser, removeProfile]
  );

  // Memoize the profile fetch logic
  const fetchProfile = useCallback(() => {
    if (userId) {
      supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
        .then(({ data }) => {
          updateProfile(data);
        });
    }
  }, [userId, updateProfile]);

  // Effect for initial session fetch
  useEffect(() => {
    fetchSessionAndUpdateUser();
  }, [fetchSessionAndUpdateUser]);

  // Effect for auth state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      handleAuthStateChange
    );

    // Cleanup subscription on unmount
    return () => {
      authListener?.subscription?.unsubscribe();
    };
  }, [handleAuthStateChange]);

  // Effect for fetching profile
  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    profile,
  };
}

// ============================================================================
// Issues Management Hook
// ============================================================================

export function useIssues() {
  const [state, setState] = useState<IssuesState>({
    issues: [],
    issuesByStatus: {},
    loading: true,
    error: null,
  });

  const [realtimeUnsubscribe, setRealtimeUnsubscribe] = useState<
    (() => void) | null
  >(null);

  // Helper function to group issues by status
  const groupIssuesByStatus = useCallback(
    (issues: Issue[]): Record<string, Issue[]> => {
      return issues.reduce(
        (acc, issue) => {
          const statusId = issue.status_id;
          if (!acc[statusId]) {
            acc[statusId] = [];
          }
          acc[statusId].push(issue);
          return acc;
        },
        {} as Record<string, Issue[]>
      );
    },
    []
  );

  // Helper function to build the complete query with joins
  const buildIssueQuery = useCallback(() => {
    return supabase.from('issues').select(`
      *,
      status:status!status_id (
        id,
        name,
        color,
        icon_name
      ),
      assignee:profiles!assignee_id (
        id,
        full_name,
        email,
        avatar_url
      ),
      priority:priorities!priority_id (
        id,
        name,
        icon_name,
        sort_order
      ),
      project:projects!project_id (
        id,
        name,
        description
      )
    `);
  }, []);

  // Transform the data to match the expected format
  const transformIssueData = useCallback((data: unknown[]): Issue[] => {
    return (data as Issue[]) || [];
  }, []);

  // Data Fetching Functions (return data directly)
  const getAllIssues = useCallback((): Issue[] => {
    return state.issues;
  }, [state.issues]);

  const getIssueById = useCallback(
    (id: string): Issue | undefined => {
      return state.issues.find((issue) => issue.id === id);
    },
    [state.issues]
  );

  const getIssuesByStatus = useCallback(
    (statusId: string): Issue[] => {
      return state.issues.filter((issue) => issue.status_id === statusId);
    },
    [state.issues]
  );

  const searchIssues = useCallback(
    (query: string): Issue[] => {
      if (!query.trim()) return [];

      const lowercaseQuery = query.toLowerCase();
      return state.issues.filter(
        (issue) =>
          issue.title.toLowerCase().includes(lowercaseQuery) ||
          issue.description.toLowerCase().includes(lowercaseQuery) ||
          issue.identifier.toLowerCase().includes(lowercaseQuery)
      );
    },
    [state.issues]
  );

  const filterIssues = useCallback(
    (filters: FilterOptions): Issue[] => {
      let filteredIssues = state.issues;

      // Filter by status
      if (filters.status && filters.status.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) => filters.status?.includes(issue.status_id) ?? false
        );
      }

      // Filter by assignee
      if (filters.assignee && filters.assignee.length > 0) {
        filteredIssues = filteredIssues.filter((issue) => {
          if (filters.assignee?.includes('unassigned')) {
            if (issue.assignee_id === null) {
              return true;
            }
          }
          return (
            issue.assignee_id &&
            (filters.assignee?.includes(issue.assignee_id) ?? false)
          );
        });
      }

      // Filter by priority
      if (filters.priority && filters.priority.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) => filters.priority?.includes(issue.priority_id) ?? false
        );
      }

      // Filter by labels
      if (filters.labels && filters.labels.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) =>
            issue.labels?.some(
              (label) => filters.labels?.includes(label.id) ?? false
            ) ?? false
        );
      }

      // Filter by project
      if (filters.project && filters.project.length > 0) {
        filteredIssues = filteredIssues.filter(
          (issue) =>
            issue.project_id &&
            (filters.project?.includes(issue.project_id) ?? false)
        );
      }

      return filteredIssues;
    },
    [state.issues]
  );

  // Fetch all issues from database
  const fetchIssues = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      const { data, error } = await buildIssueQuery().order('rank', {
        ascending: false,
      });

      if (error) throw error;

      const transformedIssues = transformIssueData(data || []);
      const groupedIssues = groupIssuesByStatus(transformedIssues);

      setState((prev) => ({
        ...prev,
        issues: transformedIssues,
        issuesByStatus: groupedIssues,
        loading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : 'Failed to fetch issues',
      }));
    }
  }, [buildIssueQuery, transformIssueData, groupIssuesByStatus]);

  // Action Functions (return Promise with resolve/reject pattern)
  const addIssue = useCallback(
    (issueInput: CreateIssueInput): Promise<Issue> => {
      return new Promise((resolve, reject) => {
        // Extract labels from issueInput to avoid inserting into issues table
        const { labels, ...issueDataWithoutLabels } = issueInput;

        // Get current user
        supabase.auth
          .getUser()
          .then(({ data: { user }, error: authError }) => {
            if (authError || !user) {
              logError(
                'addIssue - Authentication',
                authError || 'No user found'
              );
              reject(
                createStandardError(authError, ERROR_MESSAGES.NOT_AUTHENTICATED)
              );
              return;
            }

            // Generate unique identifier
            const identifier = `ISSUE-${Date.now()}`;

            const newIssue = {
              ...issueDataWithoutLabels,
              identifier,
              created_by: user.id,
            };

            // Insert the issue
            return supabase.from('issues').insert([newIssue]).select().single();
          })
          .then((result) => {
            if (!result) {
              reject(new Error('No result from insert operation'));
              return;
            }

            const { data, error } = result;
            if (error) {
              logError('addIssue - Database Insert', error, { issueInput });
              reject(
                createStandardError(error, 'Failed to create issue in database')
              );
              return;
            }

            // Add labels if provided
            if (labels && labels.length > 0) {
              const labelInserts = labels.map((labelId) => ({
                issue_id: data.id,
                label_id: labelId,
              }));

              return supabase
                .from('issue_labels')
                .insert(labelInserts)
                .then((labelResult) => {
                  if (labelResult.error) {
                    logError('addIssue - Label Insert', labelResult.error, {
                      issueId: data.id,
                      labelIds: labels,
                    });
                    throw createStandardError(
                      labelResult.error,
                      'Failed to add labels to issue'
                    );
                  }
                  return data; // Return the issue data after labels are inserted
                });
            }

            return Promise.resolve(data);
          })
          .then((issueData) => {
            if (!issueData) {
              reject(new Error('No issue data returned'));
              return;
            }

            // Create a basic issue object without complex relationships
            // to avoid schema cache issues. The realtime subscription will
            // handle updating with complete data including labels.
            const basicIssue: Issue = {
              id: issueData.id,
              identifier: issueData.identifier,
              title: issueData.title,
              description: issueData.description || '',
              status_id: issueData.status_id,
              assignee_id: issueData.assignee_id,
              priority_id: issueData.priority_id,
              project_id: issueData.project_id,
              cycle_id: issueData.cycle_id,
              parent_issue_id: issueData.parent_issue_id,
              rank: issueData.rank,
              due_date: issueData.due_date,
              created_at: issueData.created_at ?? '',
              updated_at: issueData.updated_at ?? '',
              created_by: issueData.created_by,
              // Basic labels array - will be updated by realtime
              labels: labels
                ? labels.map((labelId) => ({
                    id: labelId,
                    name: '',
                    color: '',
                  }))
                : [],
            };

            // Optimistic update with basic issue data
            setState((prev) => {
              const newIssues = [...prev.issues, basicIssue];
              return {
                ...prev,
                issues: newIssues,
                issuesByStatus: groupIssuesByStatus(newIssues),
              };
            });

            resolve(basicIssue);
          });
      });
    },
    [groupIssuesByStatus]
  );

  const updateIssue = useCallback(
    (id: string, updates: Partial<Issue>): Promise<Issue> => {
      return new Promise((resolve, reject) => {
        supabase
          .from('issues')
          .update(updates)
          .eq('id', id)
          .select()
          .single()
          .then((result) => {
            try {
              if (!result) {
                reject(new Error('No result from update operation'));
                return;
              }

              const { error } = result;
              if (error) {
                reject(error);
                return;
              }

              // Fetch the complete updated issue data
              buildIssueQuery()
                .eq('id', id)
                .single()
                .then((fetchResult) => {
                  try {
                    if (!fetchResult) {
                      reject(new Error('No result from fetch operation'));
                      return;
                    }

                    const { data: completeIssue, error: fetchError } =
                      fetchResult;
                    if (fetchError) {
                      reject(fetchError);
                      return;
                    }

                    const transformedIssue = transformIssueData([
                      completeIssue,
                    ])[0];

                    // Optimistic update
                    setState((prev) => {
                      const newIssues = prev.issues.map((issue) =>
                        issue.id === id ? transformedIssue : issue
                      );
                      return {
                        ...prev,
                        issues: newIssues,
                        issuesByStatus: groupIssuesByStatus(newIssues),
                      };
                    });

                    // Check if status changed and send email notification
                    if (
                      updates.status_id &&
                      updates.status_id !== transformedIssue.status?.id
                    ) {
                      // Get current user from store
                      const currentUser = userStore.getState().user;
                      if (currentUser) {
                        // Get old and new status information
                        Promise.all([
                          supabase
                            .from('status')
                            .select('*')
                            .eq('id', transformedIssue.status?.id || '')
                            .single(),
                          supabase
                            .from('status')
                            .select('*')
                            .eq('id', updates.status_id)
                            .single(),
                        ])
                          .then(([oldStatusResult, newStatusResult]) => {
                            if (
                              !oldStatusResult.error &&
                              !newStatusResult.error
                            ) {
                              // Get project members for recipients
                              return supabaseClient
                                .from('project_members')
                                .select(
                                  `
                                  profiles!inner(
                                    email
                                  )
                                `
                                )
                                .eq(
                                  'project_id',
                                  transformedIssue.project?.id || ''
                                )
                                .then(
                                  ({
                                    data: membersData,
                                    error: membersError,
                                  }) => {
                                    if (membersError) {
                                      console.error(
                                        'Error fetching project members:',
                                        membersError
                                      );
                                      return;
                                    }

                                    const recipients =
                                      (
                                        membersData as ProjectMemberQueryResult[]
                                      )
                                        ?.map(
                                          (member: ProjectMemberQueryResult) =>
                                            member.profiles?.email
                                        )
                                        .filter(Boolean) || [];

                                    if (recipients.length > 0) {
                                      const emailData = {
                                        issue: {
                                          id: transformedIssue.id,
                                          title: transformedIssue.title,
                                          description:
                                            transformedIssue.description,
                                          assignee: transformedIssue.assignee,
                                          priority: transformedIssue.priority,
                                          due_date: transformedIssue.due_date,
                                        },
                                        project: {
                                          id:
                                            transformedIssue.project?.id || '',
                                          name:
                                            transformedIssue.project?.name ||
                                            '',
                                        },
                                        oldStatus: oldStatusResult.data,
                                        newStatus: newStatusResult.data,
                                        triggeredBy: {
                                          id: currentUser.id,
                                          full_name:
                                            currentUser.user_metadata
                                              ?.full_name || 'Unknown User',
                                          email: currentUser.email || '',
                                        },
                                        recipients: recipients,
                                        timestamp: new Date().toISOString(),
                                        actionType: 'status_changed' as const,
                                      };

                                      return fetch(
                                        '/api/issues/status-updated',
                                        {
                                          method: 'POST',
                                          headers: {
                                            'Content-Type': 'application/json',
                                          },
                                          body: JSON.stringify(emailData),
                                        }
                                      );
                                    }
                                  }
                                );
                            }
                          })
                          .then(() => {
                            console.log(
                              'Issue status update email sent successfully'
                            );
                          })
                          .catch((error) => {
                            console.error(
                              'Failed to send issue status update email:',
                              error
                            );
                          });
                      }
                    }

                    resolve(transformedIssue);
                  } catch (fetchError) {
                    reject(
                      fetchError instanceof Error
                        ? fetchError
                        : new Error('Failed to fetch updated issue')
                    );
                  }
                });
            } catch (error) {
              reject(
                error instanceof Error
                  ? error
                  : new Error('Failed to update issue')
              );
            }
          });
      });
    },
    [buildIssueQuery, transformIssueData, groupIssuesByStatus]
  );

  const deleteIssue = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        supabase
          .from('issues')
          .delete()
          .eq('id', id)
          .then((result) => {
            try {
              if (!result) {
                reject(new Error('No result from delete operation'));
                return;
              }

              const { error } = result;
              if (error) {
                reject(error);
                return;
              }

              // Optimistic update
              setState((prev) => {
                const newIssues = prev.issues.filter(
                  (issue) => issue.id !== id
                );
                return {
                  ...prev,
                  issues: newIssues,
                  issuesByStatus: groupIssuesByStatus(newIssues),
                };
              });

              resolve();
            } catch (error) {
              reject(
                error instanceof Error
                  ? error
                  : new Error('Failed to delete issue')
              );
            }
          });
      });
    },
    [groupIssuesByStatus]
  );

  // Real-time subscriptions setup
  useEffect(() => {
    fetchIssues();

    try {
      // Set up real-time subscriptions using direct supabase client
      const issuesChannel = supabase
        .channel('issues-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'issues' },
          (payload) => {
            try {
              console.log('Issues real-time change:', payload);

              // Show notifications for different events
              if (payload.eventType === 'INSERT' && payload.new) {
                const issue = payload.new as Issue;
                notificationService.issueCreated(issue.title);

                // Send email notification using Promise chain
                Promise.resolve()
                  .then(() => {
                    // Get current user from store
                    const currentUser = userStore.getState().user;
                    if (!currentUser) {
                      throw new Error('No current user found');
                    }

                    // Get project details and recipients
                    return Promise.all([
                      supabaseClient
                        .from('projects')
                        .select('*')
                        .eq('id', issue.project_id || '')
                        .single(),
                      supabaseClient
                        .from('project_members')
                        .select(
                          `
                          profiles!inner(
                            email
                          )
                        `
                        )
                        .eq('project_id', issue.project_id || ''),
                    ]);
                  })
                  .then(([projectResult, membersResult]) => {
                    if (projectResult.error || membersResult.error) {
                      console.error('Error fetching project data:', {
                        projectError: projectResult.error,
                        membersError: membersResult.error,
                      });
                      return;
                    }

                    const projectData = projectResult.data;
                    const recipients =
                      (membersResult.data as ProjectMemberQueryResult[])
                        ?.map(
                          (member: ProjectMemberQueryResult) =>
                            member.profiles?.email
                        )
                        .filter(Boolean) || [];

                    if (recipients.length === 0) {
                      console.warn(
                        'No recipients found for issue creation email'
                      );
                      return;
                    }

                    const emailData = {
                      issue: {
                        id: issue.id,
                        title: issue.title,
                        description: issue.description,
                        status: issue.status,
                        priority: issue.priority,
                        assignee: issue.assignee,
                        due_date: issue.due_date,
                      },
                      project: {
                        id: projectData.id,
                        name: projectData.name,
                        description: projectData.description,
                      },
                      triggeredBy: {
                        id: userStore.getState().user?.id || '',
                        full_name:
                          userStore.getState().user?.user_metadata?.full_name ||
                          'Unknown User',
                        email: userStore.getState().user?.email || '',
                      },
                      recipients: recipients,
                      timestamp: new Date().toISOString(),
                      actionType: 'created' as const,
                    };

                    return fetch('/api/issues/created', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify(emailData),
                    });
                  })
                  .then((response) => {
                    if (response?.ok) {
                      console.log('Issue creation email sent successfully');
                    } else {
                      console.warn('Issue creation email failed to send');
                    }
                  })
                  .catch((error) => {
                    console.error(
                      'Failed to send issue creation email:',
                      error
                    );
                  });
              } else if (
                payload.eventType === 'UPDATE' &&
                payload.new &&
                payload.old
              ) {
                const newIssue = payload.new as Issue;
                const oldIssue = payload.old as Issue;

                if (newIssue.status_id !== oldIssue.status_id) {
                  // Status changed - we'd need to fetch status name, for now just show generic message
                  notificationService.issueUpdated(newIssue.title);
                } else if (newIssue.assignee_id !== oldIssue.assignee_id) {
                  notificationService.issueUpdated(newIssue.title);
                } else {
                  notificationService.issueUpdated(newIssue.title);
                }
              } else if (payload.eventType === 'DELETE' && payload.old) {
                const issue = payload.old as Issue;
                notificationService.issueDeleted(issue.title);
              }

              // Refetch issues to get updated data
              fetchIssues();
            } catch (error) {
              console.error('Error handling issues realtime event:', error);
              // Still try to refetch issues as fallback
              fetchIssues();
            }
          }
        )
        .subscribe();

      const issueLabelsChannel = supabase
        .channel('issue-labels-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'issue_labels' },
          (payload) => {
            try {
              console.log('Issue labels real-time change:', payload);
              // Refetch issues when label associations change
              fetchIssues();
            } catch (error) {
              console.error(
                'Error handling issue labels realtime event:',
                error
              );
              // Still try to refetch issues as fallback
              fetchIssues();
            }
          }
        )
        .subscribe();

      // Combine unsubscribe functions
      const combinedUnsubscribe = () => {
        try {
          supabase.removeChannel(issuesChannel);
          supabase.removeChannel(issueLabelsChannel);
        } catch (error) {
          console.error('Error unsubscribing from realtime:', error);
        }
      };

      setRealtimeUnsubscribe(() => combinedUnsubscribe);

      return combinedUnsubscribe;
    } catch (error) {
      console.error('Error setting up realtime subscriptions:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchIssues]);

  // Fetch issues on mount
  useEffect(() => {
    fetchIssues();
  }, [fetchIssues]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (realtimeUnsubscribe) {
        realtimeUnsubscribe();
      }
    };
  }, [realtimeUnsubscribe]);

  // Fetch issues for a specific project (for collaborators)
  const getProjectIssues = useCallback(
    (projectId: string): Promise<Issue[]> => {
      return new Promise((resolve, reject) => {
        buildIssueQuery()
          .eq('project_id', projectId)
          .order('rank', { ascending: false })
          .then(({ data, error }) => {
            if (error) {
              console.error('Error fetching project issues:', error);
              reject(error);
            } else {
              const transformedIssues = transformIssueData(data || []);
              resolve(transformedIssues);
            }
          });
      });
    },
    [buildIssueQuery, transformIssueData]
  );

  // Fetch issues for all projects a collaborator has access to
  const getCollaboratorIssues = useCallback(
    (userId: string): Promise<Issue[]> => {
      return new Promise((resolve, reject) => {
        // First get all project IDs the user has access to
        const leadProjectsQuery = supabaseClient
          .from('projects')
          .select('id')
          .eq('is_archived', false)
          .eq('lead_id', userId);

        const memberProjectsQuery = supabaseClient
          .from('project_members')
          .select('project_id')
          .eq('user_id', userId);

        const membersArrayProjectsQuery = supabaseClient
          .from('projects')
          .select('id')
          .eq('is_archived', false)
          .contains('members', [userId]);

        Promise.all([
          leadProjectsQuery,
          memberProjectsQuery,
          membersArrayProjectsQuery,
        ])
          .then(([leadResult, memberResult, membersArrayResult]) => {
            if (leadResult.error) {
              console.error('Error fetching lead projects:', leadResult.error);
              reject(leadResult.error);
              return;
            }
            if (memberResult.error) {
              console.error(
                'Error fetching member projects:',
                memberResult.error
              );
              reject(memberResult.error);
              return;
            }
            if (membersArrayResult.error) {
              console.error(
                'Error fetching members array projects:',
                membersArrayResult.error
              );
              reject(membersArrayResult.error);
              return;
            }

            // Combine and deduplicate project IDs
            const leadProjectIds = (leadResult.data || []).map((p) => p.id);
            const memberProjectIds = (memberResult.data || []).map(
              (m) => m.project_id
            );
            const membersArrayProjectIds = (membersArrayResult.data || []).map(
              (p) => p.id
            );

            const allProjectIds = [
              ...leadProjectIds,
              ...memberProjectIds,
              ...membersArrayProjectIds,
            ];
            const uniqueProjectIds = [...new Set(allProjectIds)];

            if (uniqueProjectIds.length === 0) {
              resolve([]);
              return;
            }

            // Now fetch issues for all these projects
            buildIssueQuery()
              .in('project_id', uniqueProjectIds)
              .order('rank', { ascending: false })
              .then(({ data, error }) => {
                if (error) {
                  console.error('Error fetching collaborator issues:', error);
                  reject(error);
                } else {
                  const transformedIssues = transformIssueData(data || []);
                  resolve(transformedIssues);
                }
              });
          })
          .catch((error) => {
            console.error('Error fetching collaborator issues:', error);
            reject(error);
          });
      });
    },
    [buildIssueQuery, transformIssueData]
  );

  return {
    // State
    issues: state.issues,
    issuesByStatus: state.issuesByStatus,
    loading: state.loading,
    error: state.error,

    // Data fetching functions (return data directly)
    getAllIssues,
    getIssueById,
    getIssuesByStatus,
    searchIssues,
    filterIssues,
    getProjectIssues,
    getCollaboratorIssues,

    // Action functions (return Promise with resolve/reject pattern)
    addIssue,
    updateIssue,
    deleteIssue,

    // Utility functions
    fetchIssues,
  };
}

// Hook for managing labels
export function useLabels() {
  const [labels, setLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLabels = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('labels')
        .select('*')
        .order('name');

      if (fetchError) {
        throw fetchError;
      }

      setLabels(data || []);
    } catch (err) {
      console.error('Error fetching labels:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch labels');
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchLabels();

    try {
      const labelsChannel = supabase
        .channel('labels-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'labels' },
          (payload) => {
            try {
              console.log('Labels real-time change:', payload);
              fetchLabels();
            } catch (error) {
              console.error('Error handling labels realtime event:', error);
              // Still try to refetch labels as fallback
              fetchLabels();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(labelsChannel);
        } catch (error) {
          console.error('Error unsubscribing from labels realtime:', error);
        }
      };
    } catch (error) {
      console.error('Error setting up labels realtime subscription:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchLabels]);

  return {
    labels,
    loading,
    error,
    refetch: fetchLabels,
  };
}

// Hook for managing priorities
export function usePriorities() {
  const [priorities, setPriorities] = useState<Priority[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPriorities = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('priorities')
        .select('*')
        .order('sort_order', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setPriorities(data || []);
    } catch (err) {
      console.error('Error fetching priorities:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to fetch priorities'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchPriorities();

    try {
      const prioritiesChannel = supabase
        .channel('priorities-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'priorities' },
          (payload) => {
            try {
              console.log('Priorities real-time change:', payload);
              fetchPriorities();
            } catch (error) {
              console.error('Error handling priorities realtime event:', error);
              // Still try to refetch priorities as fallback
              fetchPriorities();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(prioritiesChannel);
        } catch (error) {
          console.error('Error unsubscribing from priorities realtime:', error);
        }
      };
    } catch (error) {
      console.error(
        'Error setting up priorities realtime subscription:',
        error
      );
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchPriorities]);

  return {
    priorities,
    loading,
    error,
    refetch: fetchPriorities,
  };
}

// Enhanced hook for managing projects with full CRUD operations
export function useProjects() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all projects with joined data
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('projects')
        .select(
          `
          *,
          lead:profiles!lead_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          ),
          health:health_status!health_id(
            id,
            name,
            color,
            description
          ),
          client:clients!client_id(
            id,
            name,
            email,
            phone,
            description,
            company_name,
            industry,
            contact_person,
            address,
            created_at,
            updated_at,
            created_by,
            is_active
          ),
          budget:budgets!budget_id(
            id,
            ProjectId,
            ClientId,
            ActualAmount,
            CurrentAmount,
            Currency,
            Category,
            Status,
            has_affiliate,
            affiliateId,
            AffiliateCommission,
            has_collaborator,
            collaborators,
            expense_details,
            PayoutStatus,
            ApprovedBy,
            ApprovalDate,
            StartDate,
            EndDate,
            Notes,
            created_at,
            updated_at
          )
        `
        )
        .eq('is_archived', false)
        .order('updated_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching projects:', fetchError);
        throw fetchError;
      }

      setProjects((data as unknown as Project[]) || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch projects';
      console.error('Error fetching projects:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a new project
  const addProject = useCallback(
    (input: CreateProjectInput): Promise<Project> => {
      return new Promise((resolve, reject) => {
        const optimisticProject: Project = {
          id: `temp-${Date.now()}`,
          name: input.name,
          description: input.description || null,
          icon: input.icon || null,
          percent_complete: input.percent_complete || 0,
          start_date: input.start_date || null,
          target_date: input.target_date || null,
          lead_id: input.lead_id || null,
          status_id: input.status_id || null,
          priority_id: input.priority_id || null,
          health_id: input.health_id || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: null,
          is_archived: false,
          members: input.members || null,
          is_proposed: input.is_proposed || null,
          affiliate_user: input.affiliate_user || null,
          client_id: input.client_id || null,
          budget_id: input.budget_id || null,
        };

        // Optimistic update
        setProjects((prev) => [optimisticProject, ...prev]);

        supabaseClient
          .from('projects')
          .insert([
            {
              ...input,
            },
          ])
          .select(
            `
            *,
            lead:profiles!lead_id(
              id,
              full_name,
              email,
              avatar_url
            ),
            status:status!status_id(
              id,
              name,
              color,
              icon_name
            ),
            priority:priorities!priority_id(
              id,
              name,
              icon_name,
              sort_order
            ),
            health:health_status!health_id(
              id,
              name,
              color,
              description
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProjects((prev) =>
                prev.filter((p) => p.id !== optimisticProject.id)
              );
              reject(error);
            } else {
              // Replace optimistic project with real data
              setProjects((prev) =>
                prev.map((p) =>
                  p.id === optimisticProject.id
                    ? (data as unknown as Project)
                    : p
                )
              );
              resolve(data as unknown as Project);
            }
          });
      });
    },
    []
  );

  // Update an existing project
  const updateProject = useCallback(
    (id: string, updates: UpdateProjectInput): Promise<Project> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalProjects = [...projects];
        setProjects((prev) =>
          prev.map((p) =>
            p.id === id
              ? { ...p, ...updates, updated_at: new Date().toISOString() }
              : p
          )
        );

        supabaseClient
          .from('projects')
          .update(updates)
          .eq('id', id)
          .select(
            `
            *,
            lead:profiles!lead_id(
              id,
              full_name,
              email,
              avatar_url
            ),
            status:status!status_id(
              id,
              name,
              color,
              icon_name
            ),
            priority:priorities!priority_id(
              id,
              name,
              icon_name,
              sort_order
            ),
            health:health_status!health_id(
              id,
              name,
              color,
              description
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProjects(originalProjects);
              reject(error);
            } else {
              // Update with real data
              const updatedProject = data as unknown as Project;
              setProjects((prev) =>
                prev.map((p) => (p.id === id ? updatedProject : p))
              );

              // Check if status changed and send email notification
              if (updates.status_id) {
                const currentUser = userStore.getState().user;
                if (currentUser) {
                  // Get old and new status information
                  Promise.all([
                    supabaseClient
                      .from('status')
                      .select('*')
                      .eq(
                        'id',
                        originalProjects.find((p) => p.id === id)?.status?.id ||
                          ''
                      )
                      .single(),
                    supabaseClient
                      .from('status')
                      .select('*')
                      .eq('id', updates.status_id)
                      .single(),
                  ])
                    .then(([oldStatusResult, newStatusResult]) => {
                      if (!oldStatusResult.error && !newStatusResult.error) {
                        // Get project members for recipients
                        return supabaseClient
                          .from('project_members')
                          .select(
                            `
                            profiles!inner(
                              email
                            )
                          `
                          )
                          .eq('project_id', id)
                          .then(
                            ({ data: membersData, error: membersError }) => {
                              if (membersError) {
                                console.error(
                                  'Error fetching project members:',
                                  membersError
                                );
                                return;
                              }

                              const recipients =
                                (membersData as ProjectMemberQueryResult[])
                                  ?.map(
                                    (member: ProjectMemberQueryResult) =>
                                      member.profiles?.email
                                  )
                                  .filter(Boolean) || [];

                              if (recipients.length > 0) {
                                const emailData = {
                                  project: {
                                    id: updatedProject.id,
                                    name: updatedProject.name,
                                    description: updatedProject.description,
                                    lead: updatedProject.lead,
                                    priority: updatedProject.priority,
                                    target_date: updatedProject.target_date,
                                    percent_complete:
                                      updatedProject.percent_complete,
                                  },
                                  oldStatus: oldStatusResult.data,
                                  newStatus: newStatusResult.data,
                                  triggeredBy: {
                                    id: currentUser.id,
                                    full_name:
                                      currentUser.user_metadata?.full_name ||
                                      'Unknown User',
                                    email: currentUser.email || '',
                                  },
                                  recipients: recipients,
                                  timestamp: new Date().toISOString(),
                                  actionType: 'status_changed' as const,
                                };

                                return fetch('/api/projects/status-updated', {
                                  method: 'POST',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify(emailData),
                                });
                              }
                            }
                          );
                      }
                    })
                    .then(() => {
                      console.log(
                        'Project status update email sent successfully'
                      );
                    })
                    .catch((error) => {
                      console.error(
                        'Failed to send project status update email:',
                        error
                      );
                    });
                }
              }

              resolve(updatedProject);
            }
          });
      });
    },
    [projects]
  );

  // Delete a project (soft delete by archiving)
  const deleteProject = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalProjects = [...projects];
        setProjects((prev) => prev.filter((p) => p.id !== id));

        supabaseClient
          .from('projects')
          .update({ is_archived: true })
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setProjects(originalProjects);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [projects]
  );

  // Fetch a single project by ID
  const fetchProject = useCallback((id: string): Promise<Project | null> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('projects')
        .select(
          `
          *,
          lead:profiles!lead_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          ),
          health:health_status!health_id(
            id,
            name,
            color,
            description
          )
        `
        )
        .eq('id', id)
        .eq('is_archived', false)
        .single()
        .then(({ data, error }) => {
          if (error) {
            if (error.code === 'PGRST116') {
              // No rows returned
              resolve(null);
            } else {
              reject(error);
            }
          } else {
            resolve(data as unknown as Project);
          }
        });
    });
  }, []);

  // Duplicate an existing project
  const duplicateProject = useCallback(
    (id: string, newName?: string): Promise<Project> => {
      return new Promise((resolve, reject) => {
        // First fetch the original project
        fetchProject(id)
          .then((originalProject) => {
            if (!originalProject) {
              reject(new Error('Project not found'));
              return;
            }

            // Create the duplicate project input
            const duplicateInput: CreateProjectInput = {
              name: newName || `${originalProject.name} (Copy)`,
              description: originalProject.description,
              icon: originalProject.icon,
              percent_complete: 0, // Reset progress for duplicate
              start_date: null, // Reset dates for duplicate
              target_date: null,
              lead_id: originalProject.lead_id,
              status_id: originalProject.status_id,
              priority_id: originalProject.priority_id,
              health_id: originalProject.health_id,
            };

            // Use the existing addProject function to create the duplicate
            return addProject(duplicateInput);
          })
          .then((duplicatedProject) => {
            if (!duplicatedProject) {
              reject(new Error('Failed to duplicate project'));
              return;
            }
            resolve(duplicatedProject);
          });
      });
    },
    [fetchProject, addProject]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchProjects();

    const projectsChannel = supabase
      .channel('projects-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'projects' },
        (payload) => {
          console.log('Projects real-time change:', payload);
          fetchProjects();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(projectsChannel);
    };
  }, [fetchProjects]);

  // Fetch projects where user is lead or member (for collaborators)
  const getCollaboratorProjects = useCallback(
    (userId: string): Promise<Project[]> => {
      return new Promise((resolve, reject) => {
        // First get projects where user is lead
        const leadProjectsQuery = supabaseClient
          .from('projects')
          .select(
            `
            *,
            lead:profiles!lead_id(
              id,
              full_name,
              email,
              avatar_url
            ),
            status:status!status_id(
              id,
              name,
              color,
              icon_name
            ),
            priority:priorities!priority_id(
              id,
              name,
              icon_name,
              sort_order
            ),
            health:health_status!health_id(
              id,
              name,
              color,
              description
            )
          `
          )
          .eq('is_archived', false)
          .eq('lead_id', userId);

        // Then get projects where user is a member
        const memberProjectsQuery = supabaseClient
          .from('project_members')
          .select(
            `
            projects!inner(
              *,
              lead:profiles!lead_id(
                id,
                full_name,
                email,
                avatar_url
              ),
              status:status!status_id(
                id,
                name,
                color,
                icon_name
              ),
              priority:priorities!priority_id(
                id,
                name,
                icon_name,
                sort_order
              ),
              health:health_status!health_id(
                id,
                name,
                color,
                description
              )
            )
          `
          )
          .eq('user_id', userId)
          .eq('projects.is_archived', false);

        // Finally get projects where user is in the members array
        const membersArrayProjectsQuery = supabaseClient
          .from('projects')
          .select(
            `
            *,
            lead:profiles!lead_id(
              id,
              full_name,
              email,
              avatar_url
            ),
            status:status!status_id(
              id,
              name,
              color,
              icon_name
            ),
            priority:priorities!priority_id(
              id,
              name,
              icon_name,
              sort_order
            ),
            health:health_status!health_id(
              id,
              name,
              color,
              description
            )
          `
          )
          .eq('is_archived', false)
          .contains('members', [userId]);

        Promise.all([
          leadProjectsQuery,
          memberProjectsQuery,
          membersArrayProjectsQuery,
        ])
          .then(([leadResult, memberResult, membersArrayResult]) => {
            if (leadResult.error) {
              console.error('Error fetching lead projects:', leadResult.error);
              reject(leadResult.error);
              return;
            }
            if (memberResult.error) {
              console.error(
                'Error fetching member projects:',
                memberResult.error
              );
              reject(memberResult.error);
              return;
            }
            if (membersArrayResult.error) {
              console.error(
                'Error fetching members array projects:',
                membersArrayResult.error
              );
              reject(membersArrayResult.error);
              return;
            }

            // Combine and deduplicate projects
            const leadProjects =
              (leadResult.data as unknown as Project[]) || [];
            const memberProjects =
              (memberResult.data?.map(
                (item) => item.projects
              ) as unknown as Project[]) || [];
            const membersArrayProjects =
              (membersArrayResult.data as unknown as Project[]) || [];

            const allProjects = [
              ...leadProjects,
              ...memberProjects,
              ...membersArrayProjects,
            ];
            const uniqueProjects = allProjects.filter(
              (project, index, self) =>
                index === self.findIndex((p) => p.id === project.id)
            );

            // Sort by updated_at descending
            uniqueProjects.sort(
              (a, b) =>
                new Date(b.updated_at).getTime() -
                new Date(a.updated_at).getTime()
            );

            resolve(uniqueProjects);
          })
          .catch((error) => {
            console.error('Error fetching collaborator projects:', error);
            reject(error);
          });
      });
    },
    []
  );

  // Check if user has access to a specific project
  const checkProjectAccess = useCallback(
    (projectId: string, userId: string): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        // Check if user is project lead
        const leadCheck = supabaseClient
          .from('projects')
          .select('id')
          .eq('id', projectId)
          .eq('is_archived', false)
          .eq('lead_id', userId)
          .single();

        // Check if user is project member
        const memberCheck = supabaseClient
          .from('project_members')
          .select('id')
          .eq('project_id', projectId)
          .eq('user_id', userId)
          .single();

        // Check if user is in the members array
        const membersArrayCheck = supabaseClient
          .from('projects')
          .select('id')
          .eq('id', projectId)
          .eq('is_archived', false)
          .contains('members', [userId])
          .single();

        Promise.all([leadCheck, memberCheck, membersArrayCheck])
          .then(([leadResult, memberResult, membersArrayResult]) => {
            // User has access if they are lead, member, or in members array
            const hasLeadAccess = leadResult.data && !leadResult.error;
            const hasMemberAccess = memberResult.data && !memberResult.error;
            const hasMembersArrayAccess =
              membersArrayResult.data && !membersArrayResult.error;

            resolve(
              !!(hasLeadAccess || hasMemberAccess || hasMembersArrayAccess)
            );
          })
          .catch((error) => {
            console.error('Error checking project access:', error);
            reject(error);
          });
      });
    },
    []
  );

  return {
    projects,
    loading,
    error,
    refetch: fetchProjects,
    addProject,
    updateProject,
    deleteProject,
    fetchProject,
    duplicateProject,
    getCollaboratorProjects,
    checkProjectAccess,
  };
}

// Hook for managing status options
export function useStatus() {
  const [status, setStatus] = useState<Status[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('status')
        .select('*')
        .order('sort_order', { ascending: true });

      if (fetchError) {
        throw fetchError;
      }

      setStatus(data || []);
    } catch (err) {
      console.error('Error fetching status:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch status');
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a new status
  const addStatus = useCallback(
    (input: {
      id: string;
      name: string;
      color: string;
      icon_name?: string | null;
      sort_order?: number;
    }): Promise<Status> => {
      return new Promise((resolve, reject) => {
        const optimisticStatus: Status = {
          id: input.id,
          name: input.name,
          color: input.color,
          icon_name: input.icon_name || null,
          sort_order: input.sort_order || 0,
          created_at: new Date().toISOString(),
        };

        // Optimistic update
        setStatus((prev) =>
          [...prev, optimisticStatus].sort(
            (a, b) => (a.sort_order || 0) - (b.sort_order || 0)
          )
        );

        supabaseClient
          .from('status')
          .insert([
            {
              ...input,
            },
          ])
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setStatus((prev) =>
                prev.filter((s) => s.id !== optimisticStatus.id)
              );
              reject(error);
            } else {
              // Replace optimistic status with real data
              setStatus((prev) =>
                prev.map((s) =>
                  s.id === optimisticStatus.id ? (data as Status) : s
                )
              );
              resolve(data as Status);
            }
          });
      });
    },
    []
  );

  // Update an existing status
  const updateStatus = useCallback(
    (id: string, updates: Partial<Status>): Promise<Status> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalStatus = [...status];
        setStatus((prev) =>
          prev.map((s) => (s.id === id ? { ...s, ...updates } : s))
        );

        supabaseClient
          .from('status')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setStatus(originalStatus);
              reject(error);
            } else {
              // Update with real data
              setStatus((prev) =>
                prev.map((s) => (s.id === id ? (data as Status) : s))
              );
              resolve(data as Status);
            }
          });
      });
    },
    [status]
  );

  // Delete a status
  const deleteStatus = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalStatus = [...status];
        setStatus((prev) => prev.filter((s) => s.id !== id));

        supabaseClient
          .from('status')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setStatus(originalStatus);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [status]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchStatus();

    try {
      const statusChannel = supabase
        .channel('status-changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'status' },
          (payload) => {
            try {
              console.log('Status real-time change:', payload);
              fetchStatus();
            } catch (error) {
              console.error('Error handling status realtime event:', error);
              // Still try to refetch status as fallback
              fetchStatus();
            }
          }
        )
        .subscribe();

      return () => {
        try {
          supabase.removeChannel(statusChannel);
        } catch (error) {
          console.error('Error unsubscribing from status realtime:', error);
        }
      };
    } catch (error) {
      console.error('Error setting up status realtime subscription:', error);
      // Return a no-op function if subscription setup fails
      return () => {};
    }
  }, [fetchStatus]);

  return {
    status,
    loading,
    error,
    refetch: fetchStatus,
    addStatus,
    updateStatus,
    deleteStatus,
  };
}

// Hook for managing teams with full CRUD operations
export function useTeams() {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all teams
  const fetchTeams = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      supabaseClient
        .from('teams')
        .select('*')
        .order('name', { ascending: true })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            setTeams(data || []);
            resolve();
            setLoading(false);
          }
        });
    });
  }, []);

  // Fetch a single team by ID
  const fetchTeam = useCallback((id: string): Promise<Team | null> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('teams')
        .select('*')
        .eq('id', id)
        .single()
        .then(({ data, error }) => {
          if (error) {
            if (error.code === 'PGRST116') {
              // No rows returned
              resolve(null);
            } else {
              reject(error);
            }
          } else {
            resolve(data as Team);
          }
        });
    });
  }, []);

  // Add a new team
  const addTeam = useCallback((input: CreateTeamInput): Promise<Team> => {
    return new Promise((resolve, reject) => {
      const tempId = `temp-${Date.now()}`;
      const optimisticTeam: Team = {
        id: tempId,
        name: input.name,
        icon: input.icon || null,
        color: input.color || null,
        description: input.description || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        members: input.members || null,
        projects: input.projects || null,
      };

      // Optimistic update
      setTeams((prev) =>
        [...prev, optimisticTeam].sort((a, b) => a.name.localeCompare(b.name))
      );

      // Generate ID if not provided
      const teamData = {
        ...input,
        id:
          input.id ||
          `team-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      };

      supabaseClient
        .from('teams')
        .insert([teamData])
        .select('*')
        .single()
        .then(({ data, error }) => {
          if (error) {
            // Rollback optimistic update
            setTeams((prev) => prev.filter((t) => t.id !== optimisticTeam.id));
            reject(error);
          } else {
            // Replace optimistic team with real data
            setTeams((prev) =>
              prev.map((t) => (t.id === optimisticTeam.id ? (data as Team) : t))
            );
            resolve(data as Team);
          }
        });
    });
  }, []);

  // Update an existing team
  const updateTeam = useCallback(
    (id: string, updates: Partial<Team>): Promise<Team> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalTeams = [...teams];
        setTeams((prev) =>
          prev.map((t) =>
            t.id === id
              ? { ...t, ...updates, updated_at: new Date().toISOString() }
              : t
          )
        );

        supabaseClient
          .from('teams')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setTeams(originalTeams);
              reject(error);
            } else {
              // Update with real data
              setTeams((prev) =>
                prev.map((t) => (t.id === id ? (data as Team) : t))
              );
              resolve(data as Team);
            }
          });
      });
    },
    [teams]
  );

  // Delete a team
  const deleteTeam = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalTeams = [...teams];
        setTeams((prev) => prev.filter((t) => t.id !== id));

        supabaseClient
          .from('teams')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setTeams(originalTeams);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [teams]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchTeams();

    const teamsChannel = supabase
      .channel('teams-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'teams' },
        (payload) => {
          console.log('Teams real-time change:', payload);
          fetchTeams();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(teamsChannel);
    };
  }, [fetchTeams]);

  return {
    teams,
    loading,
    error,
    refetch: fetchTeams,
    fetchTeam,
    addTeam,
    updateTeam,
    deleteTeam,
  };
}

// Hook for managing team members with full CRUD operations
export function useTeamMembers() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all team members
  const fetchTeamMembers = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      supabaseClient
        .from('team_members')
        .select('*')
        .order('joined_at', { ascending: false })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            setTeamMembers(data || []);
            resolve();
            setLoading(false);
          }
        });
    });
  }, []);

  // Fetch team members for a specific team
  const fetchTeamMembersByTeam = useCallback(
    (teamId: string): Promise<TeamMember[]> => {
      return new Promise((resolve, reject) => {
        supabaseClient
          .from('team_members')
          .select('*')
          .eq('team_id', teamId)
          .order('joined_at', { ascending: false })
          .then(({ data, error }) => {
            if (error) {
              reject(error);
            } else {
              resolve(data || []);
            }
          });
      });
    },
    []
  );

  // Add a new team member
  const addMember = useCallback(
    (input: CreateTeamMemberInput): Promise<TeamMember> => {
      return new Promise((resolve, reject) => {
        const optimisticMember: TeamMember = {
          id: crypto.randomUUID(),
          team_id: input.team_id,
          user_id: input.user_id,
          joined: input.joined || true,
          joined_at: new Date().toISOString(),
        };

        // Optimistic update
        setTeamMembers((prev) => [optimisticMember, ...prev]);

        supabaseClient
          .from('team_members')
          .insert([{ ...input, joined_at: new Date().toISOString() }])
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setTeamMembers((prev) =>
                prev.filter((m) => m.id !== optimisticMember.id)
              );
              reject(error);
            } else {
              // Replace optimistic member with real data
              setTeamMembers((prev) =>
                prev.map((m) =>
                  m.id === optimisticMember.id ? (data as TeamMember) : m
                )
              );
              resolve(data as TeamMember);
            }
          });
      });
    },
    []
  );

  // Update a team member
  const updateMember = useCallback(
    (id: string, updates: Partial<TeamMember>): Promise<TeamMember> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...teamMembers];
        setTeamMembers((prev) =>
          prev.map((m) => (m.id === id ? { ...m, ...updates } : m))
        );

        supabaseClient
          .from('team_members')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setTeamMembers(originalMembers);
              reject(error);
            } else {
              // Update with real data
              setTeamMembers((prev) =>
                prev.map((m) => (m.id === id ? (data as TeamMember) : m))
              );
              resolve(data as TeamMember);
            }
          });
      });
    },
    [teamMembers]
  );

  // Remove a team member
  const removeMember = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...teamMembers];
        setTeamMembers((prev) => prev.filter((m) => m.id !== id));

        supabaseClient
          .from('team_members')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setTeamMembers(originalMembers);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [teamMembers]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchTeamMembers();

    const teamMembersChannel = supabase
      .channel('team-members-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'team_members' },
        (payload) => {
          console.log('Team members real-time change:', payload);
          fetchTeamMembers();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(teamMembersChannel);
    };
  }, [fetchTeamMembers]);

  return {
    teamMembers,
    loading,
    error,
    refetch: fetchTeamMembers,
    fetchTeamMembersByTeam,
    addMember,
    updateMember,
    removeMember,
  };
}

// Hook for managing cycles with full CRUD operations
export function useCycles() {
  const [cycles, setCycles] = useState<Cycle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all cycles
  const fetchCycles = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      supabaseClient
        .from('cycles')
        .select('*')
        .order('number', { ascending: false })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            setCycles(data || []);
            resolve();
            setLoading(false);
          }
        });
    });
  }, []);

  // Fetch a single cycle by ID
  const fetchCycle = useCallback((id: string): Promise<Cycle | null> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('cycles')
        .select('*')
        .eq('id', id)
        .single()
        .then(({ data, error }) => {
          if (error) {
            if (error.code === 'PGRST116') {
              resolve(null);
            } else {
              reject(error);
            }
          } else {
            resolve(data as Cycle);
          }
        });
    });
  }, []);

  // Add a new cycle
  const addCycle = useCallback((input: CreateCycleInput): Promise<Cycle> => {
    return new Promise((resolve, reject) => {
      const optimisticCycle: Cycle = {
        id: input.id,
        number: input.number,
        name: input.name,
        team_id: input.team_id || null,
        start_date: input.start_date,
        end_date: input.end_date,
        progress: input.progress || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Optimistic update
      setCycles((prev) =>
        [optimisticCycle, ...prev].sort((a, b) => b.number - a.number)
      );

      supabaseClient
        .from('cycles')
        .insert([
          {
            ...input,
          },
        ])
        .select('*')
        .single()
        .then(({ data, error }) => {
          if (error) {
            // Rollback optimistic update
            setCycles((prev) =>
              prev.filter((c) => c.id !== optimisticCycle.id)
            );
            reject(error);
          } else {
            // Replace optimistic cycle with real data
            setCycles((prev) =>
              prev.map((c) =>
                c.id === optimisticCycle.id ? (data as Cycle) : c
              )
            );
            resolve(data as Cycle);
          }
        });
    });
  }, []);

  // Update an existing cycle
  const updateCycle = useCallback(
    (id: string, updates: Partial<Cycle>): Promise<Cycle> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalCycles = [...cycles];
        setCycles((prev) =>
          prev.map((c) =>
            c.id === id
              ? { ...c, ...updates, updated_at: new Date().toISOString() }
              : c
          )
        );

        supabaseClient
          .from('cycles')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setCycles(originalCycles);
              reject(error);
            } else {
              // Update with real data
              setCycles((prev) =>
                prev.map((c) => (c.id === id ? (data as Cycle) : c))
              );
              resolve(data as Cycle);
            }
          });
      });
    },
    [cycles]
  );

  // Delete a cycle
  const deleteCycle = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalCycles = [...cycles];
        setCycles((prev) => prev.filter((c) => c.id !== id));

        supabaseClient
          .from('cycles')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setCycles(originalCycles);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [cycles]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchCycles();

    const cyclesChannel = supabase
      .channel('cycles-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'cycles' },
        (payload) => {
          console.log('Cycles real-time change:', payload);
          fetchCycles();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(cyclesChannel);
    };
  }, [fetchCycles]);

  return {
    cycles,
    loading,
    error,
    refetch: fetchCycles,
    fetchCycle,
    addCycle,
    updateCycle,
    deleteCycle,
  };
}

// Hook for managing proposals with full CRUD operations
export function useProposals(userFilter?: boolean) {
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { profile } = useProfile();

  // Fetch proposals with optional user filtering for affiliates
  const fetchProposals = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      if (userFilter && !profile?.id) {
        setLoading(false);
        setError('User not authenticated');
        reject(new Error('User not authenticated'));
        return;
      }

      setLoading(true);
      setError(null);

      let query = supabaseClient
        .from('affiliate_proposals')
        .select(
          `
          *,
          client:clients!client_id(
            id,
            name,
            email,
            phone,
            description,
            company_name,
            industry,
            contact_person,
            address,
            created_at,
            updated_at,
            created_by,
            is_active
          )
        `
        )
        .order('created_at', { ascending: false });

      // Apply user filter if requested (for affiliate users)
      if (userFilter && profile?.id) {
        query = query.eq('user_id', profile.id);
      }

      query.then(({ data, error: fetchError }) => {
        if (fetchError) {
          setError(fetchError.message);
          reject(fetchError);
        } else {
          setProposals((data as unknown as Proposal[]) || []);
          resolve();
          setLoading(false);
        }
      });
    });
  }, [userFilter, profile?.id]);

  // Add a new proposal (enhanced for affiliate users)
  const addProposal = useCallback(
    (input: CreateProposalInput): Promise<Proposal> => {
      return new Promise((resolve, reject) => {
        // For affiliate users, automatically set user_id and user_email
        const proposalInput: CreateProposalInput = {
          ...input,
          user_id: userFilter ? profile?.id || null : input.user_id || null,
          user_email: userFilter
            ? profile?.email || null
            : input.user_email || null,
        };

        const optimisticProposal: Proposal = {
          id: Date.now(), // Temporary ID for optimistic update
          created_at: new Date().toISOString(),
          user_id: proposalInput.user_id || null,
          user_email: proposalInput.user_email || null,
          affiliate_proposal: proposalInput.affiliate_proposal || null,
          is_recieved: proposalInput.is_recieved || false,
          is_approved: proposalInput.is_approved || null,
          completed: proposalInput.completed || false,
          client_name: proposalInput.client_name || null,
          client_email: proposalInput.client_email || null,
          client_phone: proposalInput.client_phone || null,
          client_description: proposalInput.client_description || null,
          client_id: proposalInput.client_id || null,
        };

        // Optimistic update
        setProposals((prev) => [optimisticProposal, ...prev]);

        supabaseClient
          .from('affiliate_proposals')
          .insert([proposalInput])
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProposals((prev) =>
                prev.filter((p) => p.id !== optimisticProposal.id)
              );
              reject(error);
            } else {
              // Replace optimistic proposal with real data
              setProposals((prev) =>
                prev.map((p) =>
                  p.id === optimisticProposal.id ? (data as Proposal) : p
                )
              );
              resolve(data as Proposal);
            }
          });
      });
    },
    [userFilter, profile]
  );

  // Update an existing proposal (enhanced for affiliate users)
  const updateProposal = useCallback(
    (id: number, updates: Partial<Proposal>): Promise<Proposal> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalProposals = [...proposals];
        setProposals((prev) =>
          prev.map((p) => (p.id === id ? { ...p, ...updates } : p))
        );

        let query = supabaseClient
          .from('affiliate_proposals')
          .update(updates)
          .eq('id', id);

        // For affiliate users, ensure they can only update their own proposals
        if (userFilter && profile?.id) {
          query = query.eq('user_id', profile.id);
        }

        query
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProposals(originalProposals);
              reject(error);
            } else {
              // Update with real data
              setProposals((prev) =>
                prev.map((p) => (p.id === id ? (data as Proposal) : p))
              );
              resolve(data as Proposal);
            }
          });
      });
    },
    [proposals, userFilter, profile?.id]
  );

  // Fetch a single proposal by ID (enhanced for affiliate users)
  const fetchProposal = useCallback(
    (id: number): Promise<Proposal | null> => {
      return new Promise((resolve, reject) => {
        let query = supabaseClient
          .from('affiliate_proposals')
          .select('*')
          .eq('id', id);

        // For affiliate users, ensure they can only fetch their own proposals
        if (userFilter && profile?.id) {
          query = query.eq('user_id', profile.id);
        }

        query.single().then(({ data, error }) => {
          if (error) {
            reject(error);
          } else {
            resolve(data as Proposal);
          }
        });
      });
    },
    [userFilter, profile?.id]
  );

  // Set up real-time subscription (enhanced for affiliate users)
  useEffect(() => {
    fetchProposals();

    const channelName = userFilter
      ? 'affiliate-proposals-changes'
      : 'proposals-changes';
    let proposalsChannel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'affiliate_proposals' },
        (payload) => {
          console.log('Proposals real-time change:', payload);
          fetchProposals();
        }
      );

    // For affiliate users, add user-specific filtering
    if (userFilter && profile?.id) {
      proposalsChannel = supabase.channel(channelName).on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'affiliate_proposals',
          filter: `user_id=eq.${profile.id}`,
        },
        (payload) => {
          console.log('Affiliate proposals real-time change:', payload);
          fetchProposals();
        }
      );
    }

    proposalsChannel.subscribe();

    return () => {
      supabase.removeChannel(proposalsChannel);
    };
  }, [fetchProposals, userFilter, profile?.id]);

  return {
    proposals,
    loading,
    error,
    refetch: fetchProposals,
    addProposal,
    updateProposal,
    fetchProposal,
  };
}

// Hook for managing members with full CRUD operations
export function useMembers() {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all members
  const fetchMembers = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      supabaseClient
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            // Cast the data to Member type with proper status field
            const members: Member[] = (data || []).map((profile) => ({
              ...profile,
              status: (profile.status as 'online' | 'offline' | 'away') || null,
            }));
            setMembers(members);
            resolve();
            setLoading(false);
          }
        });
    });
  }, []);

  // Add a new member
  const addMember = useCallback((input: CreateMemberInput): Promise<Member> => {
    return new Promise((resolve, reject) => {
      const optimisticMember: Member = {
        id: crypto.randomUUID(),
        created_at: new Date().toISOString(),
        name: input.name,
        email: input.email,
        full_name: input.full_name || null,
        avatar_url: input.avatar_url || null,
        status: input.status || 'offline',
        role: input.role || 'Collaborator',
        username: input.username || null,
        joined_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Optimistic update
      setMembers((prev) => [optimisticMember, ...prev]);

      supabaseClient
        .from('profiles')
        .insert([
          {
            ...input,
            id: optimisticMember.id,
            joined_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ])
        .select('*')
        .single()
        .then(({ data, error }) => {
          if (error) {
            // Rollback optimistic update
            setMembers((prev) =>
              prev.filter((m) => m.id !== optimisticMember.id)
            );
            reject(error);
          } else {
            // Replace optimistic member with real data
            setMembers((prev) =>
              prev.map((m) =>
                m.id === optimisticMember.id ? (data as Member) : m
              )
            );
            resolve(data as Member);
          }
        });
    });
  }, []);

  // Update an existing member
  const updateMember = useCallback(
    (id: string, updates: UpdateMemberInput): Promise<Member> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...members];
        setMembers((prev) =>
          prev.map((m) =>
            m.id === id
              ? { ...m, ...updates, updated_at: new Date().toISOString() }
              : m
          )
        );

        supabaseClient
          .from('profiles')
          .update({ ...updates, updated_at: new Date().toISOString() })
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setMembers(originalMembers);
              reject(error);
            } else {
              // Update with real data
              setMembers((prev) =>
                prev.map((m) => (m.id === id ? (data as Member) : m))
              );
              resolve(data as Member);
            }
          });
      });
    },
    [members]
  );

  // Delete a member
  const deleteMember = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...members];
        setMembers((prev) => prev.filter((m) => m.id !== id));

        supabaseClient
          .from('profiles')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setMembers(originalMembers);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [members]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchMembers();

    const membersChannel = supabase
      .channel('members-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'profiles' },
        (payload) => {
          console.log('Members real-time change:', payload);
          fetchMembers();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(membersChannel);
    };
  }, [fetchMembers]);

  return {
    members,
    loading,
    error,
    refetch: fetchMembers,
    addMember,
    updateMember,
    deleteMember,
  };
}

// Hook for managing project members with full CRUD operations
export function useProjectMembers() {
  const [projectMembers, setProjectMembers] = useState<ProjectMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all project members
  const fetchProjectMembers = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      const fetchData = async () => {
        try {
          // First get all project members
          const { data: members, error: membersError } = await supabaseClient
            .from('project_members')
            .select('*')
            .order('joined_at', { ascending: false });

          if (membersError) {
            console.error('Error fetching project members:', membersError);
            setError(membersError.message);
            reject(
              new Error(
                `Failed to fetch project members: ${membersError.message}`
              )
            );
            return;
          }

          if (!members || members.length === 0) {
            console.log('No project members found');
            setProjectMembers([]);
            resolve();
            setLoading(false);
            return;
          }

          // Get user details for each member
          const userIds = members.map((m) => m.user_id);
          const { data: users, error: usersError } = await supabaseClient
            .from('profiles')
            .select('id, full_name, email, avatar_url, role')
            .in('id', userIds);

          if (usersError) {
            console.error('Error fetching user profiles:', usersError);
            setError(usersError.message);
            reject(
              new Error(`Failed to fetch user profiles: ${usersError.message}`)
            );
            return;
          }

          // Get project details for each member
          const projectIds = [...new Set(members.map((m) => m.project_id))];
          const { data: projects, error: projectsError } = await supabaseClient
            .from('projects')
            .select('id, name')
            .in('id', projectIds);

          if (projectsError) {
            console.error('Error fetching projects:', projectsError);
            setError(projectsError.message);
            reject(
              new Error(`Failed to fetch projects: ${projectsError.message}`)
            );
            return;
          }

          // Combine the data with proper type casting
          const projectMembers: ProjectMember[] = members.map((member) => {
            const user = users?.find((u) => u.id === member.user_id);
            const project = projects?.find((p) => p.id === member.project_id);

            return {
              ...member,
              role: (member.role as 'lead' | 'member' | 'viewer') || 'member',
              user: user
                ? {
                    id: user.id,
                    full_name: user.full_name,
                    email: user.email,
                    avatar_url: user.avatar_url,
                    role: user.role,
                  }
                : null,
              project: project
                ? {
                    id: project.id,
                    name: project.name,
                  }
                : null,
            };
          });

          console.log(
            'Successfully fetched project members:',
            projectMembers.length
          );
          setProjectMembers(projectMembers);
          resolve();
          setLoading(false);
        } catch (error) {
          console.error('Unexpected error in fetchProjectMembers:', error);
          setError('An unexpected error occurred');
          reject(error);
          setLoading(false);
        }
      };

      fetchData();
    });
  }, []);

  // Fetch project members for a specific project
  const fetchProjectMembersByProject = useCallback(
    (projectId: string): Promise<ProjectMember[]> => {
      return new Promise((resolve, reject) => {
        const fetchData = async () => {
          try {
            // First get the project members
            const { data: members, error: membersError } = await supabaseClient
              .from('project_members')
              .select('*')
              .eq('project_id', projectId)
              .order('joined_at', { ascending: false });

            if (membersError) {
              console.error('Error fetching project members:', {
                error: membersError.message,
                details: membersError.details,
                hint: membersError.hint,
                code: membersError.code,
                projectId,
              });
              reject(
                new Error(
                  `Failed to fetch project members: ${membersError.message}`
                )
              );
              return;
            }

            if (!members || members.length === 0) {
              console.log('No project members found for project:', projectId);
              resolve([]);
              return;
            }

            // Get user details for each member
            const userIds = members.map((m) => m.user_id);
            const { data: users, error: usersError } = await supabaseClient
              .from('profiles')
              .select('id, full_name, email, avatar_url, role')
              .in('id', userIds);

            if (usersError) {
              console.error('Error fetching user profiles:', usersError);
              reject(
                new Error(
                  `Failed to fetch user profiles: ${usersError.message}`
                )
              );
              return;
            }

            // Get project details
            const { data: project, error: projectError } = await supabaseClient
              .from('projects')
              .select('id, name')
              .eq('id', projectId)
              .single();

            if (projectError) {
              console.error('Error fetching project details:', projectError);
              reject(
                new Error(
                  `Failed to fetch project details: ${projectError.message}`
                )
              );
              return;
            }

            // Combine the data
            const combinedData: ProjectMember[] = members.map((member) => ({
              ...member,
              role: member.role as 'lead' | 'member' | 'viewer',
              profiles: users?.find((u) => u.id === member.user_id),
              projects: project,
            }));

            console.log('Successfully fetched project members:', {
              projectId,
              memberCount: combinedData.length,
              members: combinedData.map((m) => ({
                id: m.id,
                user_id: m.user_id,
                role: m.role,
                user_name: m.profiles?.full_name,
              })),
            });

            resolve(combinedData);
          } catch (error) {
            console.error('Unexpected error fetching project members:', error);
            reject(
              new Error(
                `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
              )
            );
          }
        };

        fetchData();
      });
    },
    []
  );

  // Add a new project member
  const addProjectMember = useCallback(
    (input: CreateProjectMemberInput): Promise<ProjectMember> => {
      return new Promise((resolve, reject) => {
        const optimisticMember = {
          id: crypto.randomUUID(),
          project_id: input.project_id,
          user_id: input.user_id,
          role: input.role || 'member',
          joined_at: new Date().toISOString(),
        };

        // Optimistic update
        setProjectMembers((prev) => [optimisticMember, ...prev]);

        supabaseClient
          .from('project_members')
          .insert([{ ...input, role: input.role || 'member' }])
          .select(
            `
            *,
            user:profiles(
              id,
              full_name,
              email,
              avatar_url,
              role
            ),
            project:projects(
              id,
              name,
              description,
              target_date,
              percent_complete,
              lead:profiles!lead_id(
                id,
                full_name,
                email,
                avatar_url
              ),
              status:status!status_id(
                id,
                name,
                color,
                icon_name
              ),
              priority:priorities!priority_id(
                id,
                name,
                icon_name,
                sort_order
              )
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProjectMembers((prev) =>
                prev.filter((m) => m.id !== optimisticMember.id)
              );
              console.error('Database error adding project member:', {
                error: error.message,
                details: error.details,
                hint: error.hint,
                code: error.code,
              });
              reject(
                new Error(error.message || 'Failed to add project member')
              );
            } else {
              // Cast the data to ProjectMember type with proper role field
              const projectMember: ProjectMember = {
                ...data,
                role: (data.role as 'lead' | 'member' | 'viewer') || 'member',
              };
              // Replace optimistic member with real data
              setProjectMembers((prev) =>
                prev.map((m) =>
                  m.id === optimisticMember.id ? projectMember : m
                )
              );

              // Send email notification to the added member
              const currentUser = userStore.getState().user;

              // Type the data properly based on our query structure
              const typedData = data as unknown as ProjectMemberWithRelations;

              if (currentUser && typedData.user && typedData.project) {
                // Debug: Log the typed data structure
                console.log('📊 Project member data for email:', {
                  projectId: typedData.project.id,
                  projectName: typedData.project.name,
                  userName: typedData.user.full_name,
                  userEmail: typedData.user.email,
                  userRole: typedData.user.role,
                  hasStatus: !!typedData.project.status,
                  hasPriority: !!typedData.project.priority,
                  hasLead: !!typedData.project.lead,
                });

                const emailData = {
                  project: {
                    id: typedData.project.id,
                    name: typedData.project.name,
                    description: typedData.project.description || '',
                    lead: typedData.project.lead || null,
                    target_date: typedData.project.target_date || null,
                    percent_complete: typedData.project.percent_complete || 0,
                    status: typedData.project.status || null,
                    priority: typedData.project.priority || null,
                  },
                  member: {
                    id: typedData.user.id,
                    full_name: typedData.user.full_name || '',
                    email: typedData.user.email || '',
                    avatar_url: typedData.user.avatar_url || null,
                    role: typedData.user.role || 'Collaborator',
                  },
                  triggeredBy: {
                    id: currentUser.id,
                    full_name:
                      currentUser.user_metadata?.full_name || 'Unknown User',
                    email: currentUser.email || '',
                  },
                  recipients: [typedData.user.email].filter(Boolean),
                  timestamp: new Date().toISOString(),
                  actionType: 'member_added' as const,
                  role: projectMember.role,
                };

                // Send email using Promise chain
                fetch('/api/projects/member-added', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify(emailData),
                })
                  .then((response) => {
                    if (response.ok) {
                      console.log(
                        'Project member added email sent successfully'
                      );
                      // Show success notification
                      notificationService.projectMemberAdded(
                        typedData.user?.full_name || 'Member',
                        typedData.project?.name || 'Project'
                      );
                    } else {
                      console.warn('Project member added email failed to send');
                    }
                  })
                  .catch((error) => {
                    console.error(
                      'Failed to send project member added email:',
                      error
                    );
                  });
              }

              resolve(projectMember);
            }
          });
      });
    },
    []
  );

  // Remove a project member
  const removeProjectMember = useCallback(
    (projectId: string, userId: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...projectMembers];
        setProjectMembers((prev) =>
          prev.filter(
            (m) => !(m.project_id === projectId && m.user_id === userId)
          )
        );

        supabaseClient
          .from('project_members')
          .delete()
          .eq('project_id', projectId)
          .eq('user_id', userId)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setProjectMembers(originalMembers);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [projectMembers]
  );

  // Update a project member
  const updateProjectMember = useCallback(
    (
      id: string,
      updates: { role?: 'lead' | 'member' | 'viewer' }
    ): Promise<ProjectMember> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalMembers = [...projectMembers];
        setProjectMembers((prev) =>
          prev.map((m) => (m.id === id ? { ...m, ...updates } : m))
        );

        supabaseClient
          .from('project_members')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setProjectMembers(originalMembers);
              reject(error);
            } else {
              // Update the optimistic member with the new role
              const updatedMember = {
                ...projectMembers.find((m) => m.id === id),
                role: (data.role as 'lead' | 'member' | 'viewer') || 'member',
              } as ProjectMember;
              // Replace optimistic member with real data
              setProjectMembers((prev) =>
                prev.map((m) => (m.id === id ? updatedMember : m))
              );
              resolve(updatedMember);
            }
          });
      });
    },
    [projectMembers]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchProjectMembers();

    const projectMembersChannel = supabase
      .channel('project-members-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'project_members' },
        (payload) => {
          console.log('Project members real-time change:', payload);
          fetchProjectMembers();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(projectMembersChannel);
    };
  }, [fetchProjectMembers]);

  return {
    projectMembers,
    loading,
    error,
    refetch: fetchProjectMembers,
    fetchProjectMembersByProject,
    addProjectMember,
    updateProjectMember,
    removeProjectMember,
  };
}

// Hook for managing applications with full CRUD operations
export function useApplications() {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all applications
  const fetchApplications = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      supabaseClient
        .from('JOIN_US_TABLE')
        .select('*')
        .order('created_at', { ascending: false })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            setApplications(data || []);
            resolve();
            setLoading(false);
          }
        });
    });
  }, []);

  // Add a new application
  const addApplication = useCallback(
    (input: CreateApplicationInput): Promise<Application> => {
      return new Promise((resolve, reject) => {
        const optimisticApplication: Application = {
          id: Date.now(), // Temporary ID for optimistic update
          created_at: new Date().toISOString(),
          full_name: input.full_name || null,
          email: input.email,
          phone: input.phone || null,
          position: input.position || null,
          position_other: input.position_other || null,
          message: input.message || null,
          resume_url: input.resume_url || null,
          referer: input.referer || null,
          location: input.location || null,
          user_id: input.user_id,
          join_role: input.join_role || 'Collaborator',
          reviewed: input.reviewed || 'received',
          approved: input.approved || 'reviewing',
          is_nda_signed: input.is_nda_signed || null,
          nda_signed_date: input.nda_signed_date || null,
          dob: input.dob || null,
          interests: input.interests || null,
          skills: input.skills || null,
          areas: input.areas || null,
          other_area: input.other_area || null,
          why_join: input.why_join || null,
          hours_per_week: input.hours_per_week || null,
          past_experience: input.past_experience || null,
          available_days: input.available_days || null,
          preferred_time: input.preferred_time || null,
          equipment: input.equipment || null,
          additional_info: input.additional_info || null,
          newsletter: input.newsletter || false,
          is_vol_form_submited: input.is_vol_form_submited || false,
        };

        // Optimistic update
        setApplications((prev) => [optimisticApplication, ...prev]);

        supabaseClient
          .from('JOIN_US_TABLE')
          .insert([input])
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setApplications((prev) =>
                prev.filter((a) => a.id !== optimisticApplication.id)
              );
              reject(error);
            } else {
              // Replace optimistic application with real data
              setApplications((prev) =>
                prev.map((a) =>
                  a.id === optimisticApplication.id ? (data as Application) : a
                )
              );
              resolve(data as Application);
            }
          });
      });
    },
    []
  );

  // Update an existing application
  const updateApplication = useCallback(
    (id: number, updates: Partial<Application>): Promise<Application> => {
      return new Promise((resolve, reject) => {
        // Find the current application to get its data
        const currentApplication = applications.find((a) => a.id === id);
        if (!currentApplication) {
          reject(createStandardError('Application not found', 'NOT_FOUND'));
          return;
        }

        // Optimistic update
        const originalApplications = [...applications];
        setApplications((prev) =>
          prev.map((a) => (a.id === id ? { ...a, ...updates } : a))
        );

        // Check if this is an approval update that requires account creation
        const isApprovalUpdate =
          updates.approved === 'accepted' &&
          currentApplication.approved !== 'accepted';

        if (isApprovalUpdate && currentApplication.reviewed !== 'reviewed') {
          // Rollback optimistic update
          setApplications(originalApplications);
          reject(
            createStandardError(
              'Application must be reviewed before approval',
              'VALIDATION_ERROR'
            )
          );
          return;
        }

        // First update the JOIN_US_TABLE
        supabaseClient
          .from('JOIN_US_TABLE')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setApplications(originalApplications);
              reject(error);
              return;
            }

            const updatedApplication = data as Application;

            // If this is an approval update, create auth account and update profiles
            if (isApprovalUpdate) {
              const role = updatedApplication.join_role;
              const passwordSuffix =
                role === 'Affiliate'
                  ? '#@Aff!'
                  : role === 'Collaborator'
                    ? '#@Col!'
                    : '#@Vol!';
              const password = `${updatedApplication.full_name?.slice(0, 3)}${passwordSuffix}`;

              // Create auth account
              supabaseClient.auth
                .signUp({
                  email: updatedApplication.email,
                  password: password,
                  options: {
                    data: {
                      full_name: updatedApplication.full_name,
                    },
                  },
                })
                .then(({ data: authData, error: authError }) => {
                  if (authError) {
                    reject(
                      createStandardError(
                        `Failed to create auth account: ${authError.message}`,
                        'AUTH_ERROR'
                      )
                    );
                    return;
                  }

                  if (!authData.user?.id) {
                    reject(
                      createStandardError(
                        'No user ID returned from auth',
                        'AUTH_ERROR'
                      )
                    );
                    return;
                  }

                  // Update profiles table with the new user data and link to application
                  supabaseClient
                    .from('profiles')
                    .update({
                      full_name: updatedApplication.full_name,
                      email: updatedApplication.email,
                      role: updatedApplication.join_role,
                      users_id: updatedApplication.id, // Link to JOIN_US_TABLE.id
                      user_id: updatedApplication.user_id, // Link to JOIN_US_TABLE.user_id
                    })
                    .eq('id', authData.user.id)
                    .then(({ error: profileError }) => {
                      if (profileError) {
                        reject(
                          createStandardError(
                            `Failed to update profile: ${profileError.message}`,
                            'UPDATE_ERROR'
                          )
                        );
                        return;
                      }

                      // Send notification email based on role
                      const emailData = {
                        ...updatedApplication,
                        pass: password,
                      };
                      const emailEndpoint = `/api/${role.toLowerCase()}/approved`;

                      fetch(emailEndpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(emailData),
                      })
                        .then(() => {
                          // Update with real data
                          setApplications((prev) =>
                            prev.map((a) =>
                              a.id === id ? updatedApplication : a
                            )
                          );
                          resolve(updatedApplication);
                        })
                        .catch((emailError) => {
                          // Email failed but account was created, still resolve
                          logError(
                            'Failed to send approval email:',
                            emailError
                          );
                          setApplications((prev) =>
                            prev.map((a) =>
                              a.id === id ? updatedApplication : a
                            )
                          );
                          resolve(updatedApplication);
                        });
                    });
                });
            } else {
              // Regular update without account creation
              setApplications((prev) =>
                prev.map((a) => (a.id === id ? updatedApplication : a))
              );
              resolve(updatedApplication);
            }
          });
      });
    },
    [applications]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchApplications();

    const applicationsChannel = supabase
      .channel('applications-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'JOIN_US_TABLE' },
        (payload) => {
          console.log('Applications real-time change:', payload);
          fetchApplications();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(applicationsChannel);
    };
  }, [fetchApplications]);

  return {
    applications,
    loading,
    error,
    refetch: fetchApplications,
    addApplication,
    updateApplication,
  };
}

// Hook for managing tasks with full CRUD operations
export function useTasks() {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Build task query with all necessary joins
  const buildTaskQuery = useCallback(() => {
    return supabaseClient.from('tasks').select(`
        *,
        project:projects(
          id,
          name,
          status_id,
          priority_id
        ),
        assigned_user:profiles!assigned_to(
          id,
          full_name,
          email,
          avatar_url
        ),
        created_by_user:profiles!created_by(
          id,
          full_name,
          email,
          avatar_url
        )
      `);
  }, []);

  // Transform task data to include proper relationships
  const transformTaskData = useCallback((rawTasks: unknown[]): Task[] => {
    return (rawTasks as Task[]).map((task) => ({
      ...task,
      project: task.project || null,
      assigned_user: task.assigned_user || null,
      created_by_user: task.created_by_user || null,
    }));
  }, []);

  // Fetch all tasks
  const fetchTasks = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      setLoading(true);
      setError(null);

      buildTaskQuery()
        .eq('is_archived', false)
        .order('created_at', { ascending: false })
        .then(({ data, error: fetchError }) => {
          if (fetchError) {
            setError(fetchError.message);
            reject(fetchError);
          } else {
            const transformedTasks = transformTaskData(data || []);
            setTasks(transformedTasks);
            resolve();
          }
          setLoading(false);
        });
    });
  }, [buildTaskQuery, transformTaskData]);

  // Fetch tasks for all projects a collaborator has access to
  const getCollaboratorTasks = useCallback(
    (userId: string): Promise<Task[]> => {
      return new Promise((resolve, reject) => {
        // First get all project IDs the user has access to
        const leadProjectsQuery = supabaseClient
          .from('projects')
          .select('id')
          .eq('is_archived', false)
          .eq('lead_id', userId);

        const memberProjectsQuery = supabaseClient
          .from('project_members')
          .select('project_id')
          .eq('user_id', userId);

        const membersArrayProjectsQuery = supabaseClient
          .from('projects')
          .select('id')
          .eq('is_archived', false)
          .contains('members', [userId]);

        Promise.all([
          leadProjectsQuery,
          memberProjectsQuery,
          membersArrayProjectsQuery,
        ])
          .then(([leadResult, memberResult, membersArrayResult]) => {
            if (leadResult.error) {
              console.error('Error fetching lead projects:', leadResult.error);
              reject(leadResult.error);
              return;
            }
            if (memberResult.error) {
              console.error(
                'Error fetching member projects:',
                memberResult.error
              );
              reject(memberResult.error);
              return;
            }
            if (membersArrayResult.error) {
              console.error(
                'Error fetching members array projects:',
                membersArrayResult.error
              );
              reject(membersArrayResult.error);
              return;
            }

            // Combine and deduplicate project IDs
            const leadProjectIds = (leadResult.data || []).map((p) => p.id);
            const memberProjectIds = (memberResult.data || []).map(
              (m) => m.project_id
            );
            const membersArrayProjectIds = (membersArrayResult.data || []).map(
              (p) => p.id
            );

            const allProjectIds = [
              ...leadProjectIds,
              ...memberProjectIds,
              ...membersArrayProjectIds,
            ];
            const uniqueProjectIds = [...new Set(allProjectIds)];

            if (uniqueProjectIds.length === 0) {
              resolve([]);
              return;
            }

            // Now fetch tasks for all these projects
            buildTaskQuery()
              .in('project_id', uniqueProjectIds)
              .eq('is_archived', false)
              .order('created_at', { ascending: false })
              .then(({ data, error }) => {
                if (error) {
                  console.error('Error fetching collaborator tasks:', error);
                  reject(error);
                } else {
                  const transformedTasks = transformTaskData(data || []);
                  resolve(transformedTasks);
                }
              });
          })
          .catch((error) => {
            console.error('Error fetching collaborator tasks:', error);
            reject(error);
          });
      });
    },
    [buildTaskQuery, transformTaskData]
  );

  // Add task
  const addTask = useCallback(
    (input: CreateTaskInput): Promise<Task> => {
      return new Promise((resolve, reject) => {
        supabaseClient
          .from('tasks')
          .insert([input])
          .select(
            `
            *,
            project:projects(
              id,
              name,
              status_id,
              priority_id
            ),
            assigned_user:profiles!assigned_to(
              id,
              full_name,
              email,
              avatar_url
            ),
            created_by_user:profiles!created_by(
              id,
              full_name,
              email,
              avatar_url
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              reject(error);
            } else {
              const transformedTask = transformTaskData([data])[0];
              setTasks((prev) => [transformedTask, ...prev]);
              resolve(transformedTask);
            }
          });
      });
    },
    [transformTaskData]
  );

  // Update task
  const updateTask = useCallback(
    (id: string, updates: Partial<Task>): Promise<Task> => {
      return new Promise((resolve, reject) => {
        // Store original tasks for rollback
        const originalTasks = tasks;

        // Optimistic update
        setTasks((prev) =>
          prev.map((task) => (task.id === id ? { ...task, ...updates } : task))
        );

        supabaseClient
          .from('tasks')
          .update(updates)
          .eq('id', id)
          .select(
            `
            *,
            project:projects(
              id,
              name,
              status_id,
              priority_id
            ),
            assigned_user:profiles!assigned_to(
              id,
              full_name,
              email,
              avatar_url
            ),
            created_by_user:profiles!created_by(
              id,
              full_name,
              email,
              avatar_url
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setTasks(originalTasks);
              reject(error);
            } else {
              // Update with real data
              const transformedTask = transformTaskData([data])[0];
              setTasks((prev) =>
                prev.map((task) => (task.id === id ? transformedTask : task))
              );
              resolve(transformedTask);
            }
          });
      });
    },
    [tasks, transformTaskData]
  );

  // Delete task
  const deleteTask = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Store original tasks for rollback
        const originalTasks = tasks;

        // Optimistic update
        setTasks((prev) => prev.filter((task) => task.id !== id));

        supabaseClient
          .from('tasks')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setTasks(originalTasks);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [tasks]
  );

  // Set up real-time subscription
  useEffect(() => {
    fetchTasks();

    const tasksChannel = supabase
      .channel('tasks-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'tasks' },
        (payload) => {
          console.log('Tasks real-time change:', payload);
          fetchTasks();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(tasksChannel);
    };
  }, [fetchTasks]);

  return {
    // State
    tasks,
    loading,
    error,

    // Data fetching functions
    getCollaboratorTasks,

    // Action functions (return Promise with resolve/reject pattern)
    addTask,
    updateTask,
    deleteTask,

    // Utility functions
    fetchTasks,
  };
}

// Portfolio Hook
export function usePortfolio() {
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = userStore();

  const fetchPortfolioData = useCallback((): Promise<PortfolioData | null> => {
    return new Promise((resolve, reject) => {
      if (!user?.id) {
        setLoading(false);
        setError('User not authenticated');
        reject(createStandardError('User not authenticated', 'UNAUTHORIZED'));
        return;
      }

      setLoading(true);
      setError(null);

      // Fetch profile data first
      supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()
        .then(({ data: profileData, error: profileError }) => {
          if (profileError) {
            setError(`Failed to fetch profile: ${profileError.message}`);
            setLoading(false);
            reject(
              createStandardError(
                `Failed to fetch profile: ${profileError.message}`,
                'FETCH_ERROR'
              )
            );
            return;
          }

          // Fetch JOIN_US_TABLE data using the new relationship
          // Use profiles.users_id to match with JOIN_US_TABLE.id
          if (profileData.users_id) {
            supabaseClient
              .from('JOIN_US_TABLE')
              .select('*')
              .eq('id', profileData.users_id)
              .single()
              .then(({ data: joinUsData, error: joinUsError }) => {
                // PGRST116 is "not found" error, which is acceptable for JOIN_US_TABLE
                if (joinUsError && joinUsError.code !== 'PGRST116') {
                  setError(
                    `Failed to fetch professional data: ${joinUsError.message}`
                  );
                  setLoading(false);
                  reject(
                    createStandardError(
                      `Failed to fetch professional data: ${joinUsError.message}`,
                      'FETCH_ERROR'
                    )
                  );
                  return;
                }

                // Process the data with the fetched JOIN_US_TABLE record
                processPortfolioData(profileData, joinUsData);
              });
          } else {
            // No users_id in profile, process with null JOIN_US_TABLE data
            processPortfolioData(profileData, null);
          }
        });

      // Helper function to process the combined data
      const processPortfolioData = (
        profileData: Profile_Types,
        joinUsData: JoinUsTable_Types | null
      ) => {
        // Log NDA status for debugging (can be removed in production)
        if (process.env.NODE_ENV === 'development') {
          console.log('Portfolio data fetch:', {
            userId: user.id,
            hasJoinUsData: !!joinUsData,
            is_nda_signed: joinUsData?.is_nda_signed,
            role: profileData.role,
          });
        }

        // Combine the data
        const combinedData: PortfolioData = {
          // Basic profile information
          id: profileData.id,
          name: profileData.name,
          email: profileData.email,
          avatar_url: profileData.avatar_url,
          status: profileData.status,
          role: profileData.role,
          joined_date: profileData.joined_date,

          // Professional information from JOIN_US_TABLE (with fallbacks)
          full_name: joinUsData?.full_name || profileData.full_name,
          phone: joinUsData?.phone || null,
          location: joinUsData?.location || null,
          position: joinUsData?.position || null,
          position_other: joinUsData?.position_other || null,
          skills: joinUsData?.skills || null,
          interests: joinUsData?.interests || null,
          past_experience: joinUsData?.past_experience || null,
          why_join: joinUsData?.why_join || null,
          hours_per_week: joinUsData?.hours_per_week || null,
          available_days: joinUsData?.available_days || null,
          preferred_time: joinUsData?.preferred_time || null,
          equipment: joinUsData?.equipment || null,
          areas: joinUsData?.areas || null,
          other_area: joinUsData?.other_area || null,
          additional_info: joinUsData?.additional_info || null,
          resume_url: joinUsData?.resume_url || null,
          dob: joinUsData?.dob || null,
          message: joinUsData?.message || null,

          // NDA and approval status - CRITICAL: Ensure proper boolean handling
          is_nda_signed: Boolean(joinUsData?.is_nda_signed),
          nda_signed_date: joinUsData?.nda_signed_date || null,
          approved: joinUsData?.approved || 'reviewing',
          reviewed: joinUsData?.reviewed || 'received',

          // Metadata
          created_at:
            joinUsData?.created_at ||
            profileData.created_at ||
            new Date().toISOString(),
          updated_at: profileData.updated_at,
        };

        // Log combined data for debugging (can be removed in production)
        if (process.env.NODE_ENV === 'development') {
          console.log('Combined portfolio data NDA status:', {
            is_nda_signed: combinedData.is_nda_signed,
            nda_signed_date: combinedData.nda_signed_date,
          });
        }

        setPortfolioData(combinedData);
        setLoading(false);
        resolve(combinedData);
      };
    });
  }, [user?.id]);

  const updatePortfolioData = useCallback(
    (updates: Partial<PortfolioData>): Promise<PortfolioData> => {
      return new Promise((resolve, reject) => {
        if (!user?.id) {
          reject(createStandardError('User not authenticated', 'UNAUTHORIZED'));
          return;
        }

        // Separate updates for profiles and JOIN_US_TABLE
        const profileUpdates: Partial<Profile_Types> = {};
        const joinUsUpdates: Partial<JoinUsTable_Types> = {};

        // Map updates to appropriate tables
        if (updates.name !== undefined) profileUpdates.name = updates.name;
        if (updates.avatar_url !== undefined)
          profileUpdates.avatar_url = updates.avatar_url;
        if (updates.status !== undefined)
          profileUpdates.status = updates.status;
        if (updates.full_name !== undefined)
          profileUpdates.full_name = updates.full_name;

        // Professional information goes to JOIN_US_TABLE
        if (updates.phone !== undefined) joinUsUpdates.phone = updates.phone;
        if (updates.location !== undefined)
          joinUsUpdates.location = updates.location;
        if (updates.position !== undefined)
          joinUsUpdates.position = updates.position;
        if (updates.position_other !== undefined)
          joinUsUpdates.position_other = updates.position_other;
        if (updates.skills !== undefined) joinUsUpdates.skills = updates.skills;
        if (updates.interests !== undefined)
          joinUsUpdates.interests = updates.interests;
        if (updates.past_experience !== undefined)
          joinUsUpdates.past_experience = updates.past_experience;
        if (updates.why_join !== undefined)
          joinUsUpdates.why_join = updates.why_join;
        if (updates.hours_per_week !== undefined)
          joinUsUpdates.hours_per_week = updates.hours_per_week;
        if (updates.available_days !== undefined)
          joinUsUpdates.available_days = updates.available_days;
        if (updates.preferred_time !== undefined)
          joinUsUpdates.preferred_time = updates.preferred_time;
        if (updates.equipment !== undefined)
          joinUsUpdates.equipment = updates.equipment;
        if (updates.areas !== undefined) joinUsUpdates.areas = updates.areas;
        if (updates.other_area !== undefined)
          joinUsUpdates.other_area = updates.other_area;
        if (updates.additional_info !== undefined)
          joinUsUpdates.additional_info = updates.additional_info;
        if (updates.dob !== undefined) joinUsUpdates.dob = updates.dob;
        if (updates.message !== undefined)
          joinUsUpdates.message = updates.message;

        // Helper function to fetch updated data
        const fetchUpdatedData = () => {
          fetchPortfolioData()
            .then((updatedData) => {
              if (!updatedData) {
                reject(
                  createStandardError(
                    'Failed to fetch updated portfolio data',
                    'FETCH_ERROR'
                  )
                );
                return;
              }
              resolve(updatedData);
            })
            .catch((err) => {
              logError('Error fetching updated portfolio data:', err);
              reject(err);
            });
        };

        // Update profiles table if there are profile updates
        if (Object.keys(profileUpdates).length > 0) {
          supabaseClient
            .from('profiles')
            .update({ ...profileUpdates, updated_at: new Date().toISOString() })
            .eq('id', user.id)
            .then(({ error: profileError }) => {
              if (profileError) {
                reject(
                  createStandardError(
                    `Failed to update profile: ${profileError.message}`,
                    'UPDATE_ERROR'
                  )
                );
                return;
              }

              // Continue to JOIN_US_TABLE update or fetch updated data
              if (Object.keys(joinUsUpdates).length > 0) {
                supabaseClient
                  .from('JOIN_US_TABLE')
                  .upsert({
                    user_id: user.id,
                    email: user.email || '',
                    ...joinUsUpdates,
                  })
                  .then(({ error: joinUsError }) => {
                    if (joinUsError) {
                      reject(
                        createStandardError(
                          `Failed to update professional data: ${joinUsError.message}`,
                          'UPDATE_ERROR'
                        )
                      );
                      return;
                    }
                    fetchUpdatedData();
                  });
              } else {
                fetchUpdatedData();
              }
            });
        } else if (Object.keys(joinUsUpdates).length > 0) {
          // Only JOIN_US_TABLE updates
          supabaseClient
            .from('JOIN_US_TABLE')
            .upsert({
              user_id: user.id,
              email: user.email || '',
              ...joinUsUpdates,
            })
            .then(({ error: joinUsError }) => {
              if (joinUsError) {
                reject(
                  createStandardError(
                    `Failed to update professional data: ${joinUsError.message}`,
                    'UPDATE_ERROR'
                  )
                );
                return;
              }
              fetchUpdatedData();
            });
        } else {
          // No updates to perform, return current data
          if (portfolioData) {
            resolve(portfolioData);
          } else {
            fetchUpdatedData();
          }
        }
      });
    },
    [user?.id, user?.email, fetchPortfolioData, portfolioData]
  );

  const signNDA = useCallback((): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!user?.id || !user?.email) {
        reject(createStandardError('User not authenticated', 'UNAUTHORIZED'));
        return;
      }

      // First, check if a record exists for this user by email or user_id
      supabaseClient
        .from('JOIN_US_TABLE')
        .select('id, user_id')
        .or(`email.eq.${user.email},user_id.eq.${user.id}`)
        .single()
        .then(({ data: existingRecord, error: fetchError }) => {
          if (fetchError && fetchError.code !== 'PGRST116') {
            // PGRST116 is "not found" error
            reject(
              createStandardError(
                `Failed to check existing record: ${fetchError.message}`,
                'FETCH_ERROR'
              )
            );
            return;
          }

          if (existingRecord) {
            // Update existing record - use email as primary identifier
            supabaseClient
              .from('JOIN_US_TABLE')
              .update({
                user_id: user.id, // Ensure user_id is set
                is_nda_signed: true,
                nda_signed_date: new Date().toISOString(),
              })
              .eq('id', existingRecord.id)
              .then(({ error }) => {
                if (error) {
                  reject(
                    createStandardError(
                      `Failed to sign NDA: ${error.message}`,
                      'UPDATE_ERROR'
                    )
                  );
                  return;
                }
                // Refresh portfolio data after successful NDA signing
                fetchPortfolioData()
                  .then(() => resolve())
                  .catch(() => resolve()); // Still resolve even if fetch fails
              });
          } else {
            // Insert new record
            supabaseClient
              .from('JOIN_US_TABLE')
              .insert({
                user_id: user.id || '',
                email: user.email || '',
                is_nda_signed: true,
                nda_signed_date: new Date().toISOString(),
              })
              .select('id')
              .single()
              .then(({ data: newRecord, error }) => {
                if (error) {
                  reject(
                    createStandardError(
                      `Failed to sign NDA: ${error.message}`,
                      'INSERT_ERROR'
                    )
                  );
                  return;
                }

                // Update profile to link to the new JOIN_US_TABLE record
                supabaseClient
                  .from('profiles')
                  .update({ users_id: newRecord.id })
                  .eq('id', user.id)
                  .then(({ error: profileError }) => {
                    if (profileError) {
                      console.warn(
                        'Failed to link profile to JOIN_US_TABLE:',
                        profileError
                      );
                    }

                    // Refresh portfolio data after successful NDA signing
                    fetchPortfolioData()
                      .then(() => resolve())
                      .catch(() => resolve()); // Still resolve even if fetch fails
                  });
              });
          }
        });
    });
  }, [user?.id, user?.email, fetchPortfolioData]);

  // Load portfolio data on mount and user change
  useEffect(() => {
    if (!user?.id) {
      setPortfolioData(null);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    fetchPortfolioData()
      .then((data) => {
        setPortfolioData(data);
      })
      .catch((err) => {
        setError(err.message || ERROR_MESSAGES.UNKNOWN_ERROR);
        setPortfolioData(null);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [user?.id, fetchPortfolioData]);

  // Real-time subscriptions
  useEffect(() => {
    if (!user?.id) return;

    const profilesSubscription = supabaseClient
      .channel('portfolio-profiles-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${user.id}`,
        },
        () => {
          if (process.env.NODE_ENV === 'development') {
            console.log('Profiles subscription triggered for user:', user.id);
          }
          fetchPortfolioData()
            .then((data) => {
              setPortfolioData(data);
              if (process.env.NODE_ENV === 'development') {
                console.log('Portfolio data updated via profiles subscription');
              }
            })
            .catch((err) => {
              logError('Error refreshing portfolio data:', err);
            });
        }
      )
      .subscribe();

    // Subscribe to JOIN_US_TABLE changes for all records since we need to check
    // if any changes affect the current user's linked application
    const joinUsSubscription = supabaseClient
      .channel('portfolio-joinus-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'JOIN_US_TABLE',
        },
        (payload) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('JOIN_US_TABLE subscription triggered:', payload);
          }

          // Check if this change affects the current user by refetching portfolio data
          // This will automatically use the correct relationship via profiles.users_id
          fetchPortfolioData()
            .then((data) => {
              setPortfolioData(data);
              if (process.env.NODE_ENV === 'development') {
                console.log(
                  'Portfolio data updated via JOIN_US_TABLE subscription'
                );
              }
            })
            .catch((err) => {
              logError('Error refreshing portfolio data:', err);
            });
        }
      )
      .subscribe();

    return () => {
      profilesSubscription.unsubscribe();
      joinUsSubscription.unsubscribe();
    };
  }, [user?.id, fetchPortfolioData]);

  return {
    portfolioData,
    loading,
    error,
    refetch: fetchPortfolioData,
    updatePortfolio: updatePortfolioData,
    signNDA,
  };
}

// Hook for managing clients with full CRUD operations
export function useClients() {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all clients
  const fetchClients = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('clients')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching clients:', fetchError);
        throw fetchError;
      }

      setClients((data as Client[]) || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch clients';
      console.error('Error fetching clients:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a new client
  const addClient = useCallback((input: CreateClientInput): Promise<Client> => {
    return new Promise((resolve, reject) => {
      const optimisticClient: Client = {
        id: `temp-${Date.now()}`,
        name: input.name,
        email: input.email || null,
        phone: input.phone || null,
        description: input.description || null,
        company_name: input.company_name || null,
        industry: input.industry || null,
        contact_person: input.contact_person || null,
        address: input.address || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: input.created_by || null,
        is_active: input.is_active ?? true,
      };

      // Optimistic update
      setClients((prev) => [optimisticClient, ...prev]);

      supabaseClient
        .from('clients')
        .insert([input])
        .select('*')
        .single()
        .then(({ data, error }) => {
          if (error) {
            // Rollback optimistic update
            setClients((prev) =>
              prev.filter((c) => c.id !== optimisticClient.id)
            );
            reject(error);
          } else {
            // Replace optimistic client with real data
            setClients((prev) =>
              prev.map((c) =>
                c.id === optimisticClient.id ? (data as Client) : c
              )
            );
            resolve(data as Client);
          }
        });
    });
  }, []);

  // Update an existing client
  const updateClient = useCallback(
    (id: string, updates: UpdateClientInput): Promise<Client> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalClients = [...clients];
        setClients((prev) =>
          prev.map((c) =>
            c.id === id
              ? { ...c, ...updates, updated_at: new Date().toISOString() }
              : c
          )
        );

        supabaseClient
          .from('clients')
          .update(updates)
          .eq('id', id)
          .select('*')
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setClients(originalClients);
              reject(error);
            } else {
              // Update with real data
              setClients((prev) =>
                prev.map((c) => (c.id === id ? (data as Client) : c))
              );
              resolve(data as Client);
            }
          });
      });
    },
    [clients]
  );

  // Delete a client (soft delete by setting is_active to false)
  const deleteClient = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalClients = [...clients];
        setClients((prev) =>
          prev.map((c) => (c.id === id ? { ...c, is_active: false } : c))
        );

        supabaseClient
          .from('clients')
          .update({ is_active: false })
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setClients(originalClients);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [clients]
  );

  // Fetch a single client by ID
  const fetchClient = useCallback((id: string): Promise<Client | null> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('clients')
        .select('*')
        .eq('id', id)
        .eq('is_active', true)
        .single()
        .then(({ data, error }) => {
          if (error) {
            if (error.code === 'PGRST116') {
              // No rows returned
              resolve(null);
            } else {
              reject(error);
            }
          } else {
            resolve(data as Client);
          }
        });
    });
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchClients();

    const clientsChannel = supabase
      .channel('clients-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'clients' },
        (payload) => {
          console.log('Clients real-time change:', payload);
          fetchClients();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(clientsChannel);
    };
  }, [fetchClients]);

  return {
    clients,
    loading,
    error,
    refetch: fetchClients,
    addClient,
    updateClient,
    deleteClient,
    fetchClient,
  };
}

// Hook for managing budgets with full CRUD operations
export function useBudgets() {
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch all budgets with joined data
  const fetchBudgets = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabaseClient
        .from('budgets')
        .select(
          `
          *,
          project:projects!ProjectId(
            id,
            name,
            description,
            icon,
            percent_complete,
            start_date,
            target_date,
            lead_id,
            status_id,
            priority_id,
            health_id,
            created_at,
            updated_at,
            created_by,
            is_archived,
            members,
            is_proposed,
            affiliate_user,
            client_id,
            budget_id
          ),
          client:clients!ClientId(
            id,
            name,
            email,
            phone,
            description,
            company_name,
            industry,
            contact_person,
            address,
            created_at,
            updated_at,
            created_by,
            is_active
          ),
          affiliate:profiles!affiliateId(
            id,
            full_name,
            email,
            avatar_url,
            role,
            status,
            joined_date,
            created_at,
            updated_at
          ),
          approved_by_user:profiles!ApprovedBy(
            id,
            full_name,
            email,
            avatar_url,
            role,
            status,
            joined_date,
            created_at,
            updated_at
          )
        `
        )
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching budgets:', fetchError);
        throw fetchError;
      }

      setBudgets((data as unknown as Budget[]) || []);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch budgets';
      console.error('Error fetching budgets:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a new budget
  const addBudget = useCallback((input: CreateBudgetInput): Promise<Budget> => {
    return new Promise((resolve, reject) => {
      const optimisticBudget: Budget = {
        id: `temp-${Date.now()}`,
        ProjectId: input.ProjectId,
        ClientId: input.ClientId || null,
        ActualAmount: input.ActualAmount,
        CurrentAmount: input.CurrentAmount,
        Currency: input.Currency || 'USD',
        Category: input.Category || 'development',
        Status: input.Status || 'draft',
        has_affiliate: input.has_affiliate || false,
        affiliateId: input.affiliateId || null,
        AffiliateCommission: input.AffiliateCommission || null,
        has_collaborator: input.has_collaborator || false,
        collaborators: input.collaborators || null,
        expense_details: input.expense_details || null,
        PayoutStatus: input.PayoutStatus || null,
        ApprovedBy: input.ApprovedBy || null,
        ApprovalDate: input.ApprovalDate || null,
        StartDate: input.StartDate,
        EndDate: input.EndDate,
        Notes: input.Notes || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Optimistic update
      setBudgets((prev) => [optimisticBudget, ...prev]);

      const budgetInput = {
        ...input,
        Currency: input.Currency || 'USD',
        Category: input.Category || 'development',
        Status: input.Status || 'draft',
        has_affiliate: input.has_affiliate || false,
        has_collaborator: input.has_collaborator || false,
      };

      supabaseClient
        .from('budgets')
        .insert([budgetInput])
        .select(
          `
            *,
            project:projects!ProjectId(
              id,
              name,
              description,
              icon,
              percent_complete,
              start_date,
              target_date,
              lead_id,
              status_id,
              priority_id,
              health_id,
              created_at,
              updated_at,
              created_by,
              is_archived,
              members,
              is_proposed,
              affiliate_user,
              client_id,
              budget_id
            ),
            client:clients!ClientId(
              id,
              name,
              email,
              phone,
              description,
              company_name,
              industry,
              contact_person,
              address,
              created_at,
              updated_at,
              created_by,
              is_active
            ),
            affiliate:profiles!affiliateId(
              id,
              full_name,
              email,
              avatar_url,
              role,
              status,
              joined_date,
              created_at,
              updated_at
            ),
            approved_by_user:profiles!ApprovedBy(
              id,
              full_name,
              email,
              avatar_url,
              role,
              status,
              joined_date,
              created_at,
              updated_at
            )
          `
        )
        .single()
        .then(({ data, error }) => {
          if (error) {
            // Rollback optimistic update
            setBudgets((prev) =>
              prev.filter((b) => b.id !== optimisticBudget.id)
            );
            reject(error);
          } else {
            // Replace optimistic budget with real data
            setBudgets((prev) =>
              prev.map((b) =>
                b.id === optimisticBudget.id ? (data as unknown as Budget) : b
              )
            );
            resolve(data as unknown as Budget);
          }
        });
    });
  }, []);

  // Update an existing budget
  const updateBudget = useCallback(
    (id: string, updates: UpdateBudgetInput): Promise<Budget> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalBudgets = [...budgets];
        setBudgets((prev) =>
          prev.map((b) =>
            b.id === id
              ? { ...b, ...updates, updated_at: new Date().toISOString() }
              : b
          )
        );

        supabaseClient
          .from('budgets')
          .update(updates)
          .eq('id', id)
          .select(
            `
            *,
            project:projects!ProjectId(
              id,
              name,
              description,
              icon,
              percent_complete,
              start_date,
              target_date,
              lead_id,
              status_id,
              priority_id,
              health_id,
              created_at,
              updated_at,
              created_by,
              is_archived,
              members,
              is_proposed,
              affiliate_user,
              client_id,
              budget_id
            ),
            client:clients!ClientId(
              id,
              name,
              email,
              phone,
              description,
              company_name,
              industry,
              contact_person,
              address,
              created_at,
              updated_at,
              created_by,
              is_active
            ),
            affiliate:profiles!affiliateId(
              id,
              full_name,
              email,
              avatar_url,
              role,
              status,
              joined_date,
              created_at,
              updated_at
            ),
            approved_by_user:profiles!ApprovedBy(
              id,
              full_name,
              email,
              avatar_url,
              role,
              status,
              joined_date,
              created_at,
              updated_at
            )
          `
          )
          .single()
          .then(({ data, error }) => {
            if (error) {
              // Rollback optimistic update
              setBudgets(originalBudgets);
              reject(error);
            } else {
              // Update with real data
              setBudgets((prev) =>
                prev.map((b) => (b.id === id ? (data as unknown as Budget) : b))
              );
              resolve(data as unknown as Budget);
            }
          });
      });
    },
    [budgets]
  );

  // Delete a budget
  const deleteBudget = useCallback(
    (id: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        // Optimistic update
        const originalBudgets = [...budgets];
        setBudgets((prev) => prev.filter((b) => b.id !== id));

        supabaseClient
          .from('budgets')
          .delete()
          .eq('id', id)
          .then(({ error }) => {
            if (error) {
              // Rollback optimistic update
              setBudgets(originalBudgets);
              reject(error);
            } else {
              resolve();
            }
          });
      });
    },
    [budgets]
  );

  // Fetch a single budget by ID
  const fetchBudget = useCallback((id: string): Promise<Budget | null> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('budgets')
        .select(
          `
          *,
          project:projects!ProjectId(
            id,
            name,
            description,
            icon,
            percent_complete,
            start_date,
            target_date,
            lead_id,
            status_id,
            priority_id,
            health_id,
            created_at,
            updated_at,
            created_by,
            is_archived,
            members,
            is_proposed,
            affiliate_user,
            client_id,
            budget_id
          ),
          client:clients!ClientId(
            id,
            name,
            email,
            phone,
            description,
            company_name,
            industry,
            contact_person,
            address,
            created_at,
            updated_at,
            created_by,
            is_active
          ),
          affiliate:profiles!affiliateId(
            id,
            full_name,
            email,
            avatar_url,
            role,
            status,
            joined_date,
            created_at,
            updated_at
          ),
          approved_by_user:profiles!ApprovedBy(
            id,
            full_name,
            email,
            avatar_url,
            role,
            status,
            joined_date,
            created_at,
            updated_at
          )
        `
        )
        .eq('id', id)
        .single()
        .then(({ data, error }) => {
          if (error) {
            if (error.code === 'PGRST116') {
              // No rows returned
              resolve(null);
            } else {
              reject(error);
            }
          } else {
            resolve(data as unknown as Budget);
          }
        });
    });
  }, []);

  // Set up real-time subscription
  useEffect(() => {
    fetchBudgets();

    const budgetsChannel = supabase
      .channel('budgets-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'budgets' },
        (payload) => {
          console.log('Budgets real-time change:', payload);
          fetchBudgets();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(budgetsChannel);
    };
  }, [fetchBudgets]);

  // Approve a budget
  const approveBudget = useCallback(
    (id: string, approvedBy: string): Promise<Budget> => {
      return new Promise((resolve, reject) => {
        const updates: UpdateBudgetInput = {
          Status: 'approved',
          ApprovedBy: approvedBy,
          ApprovalDate: new Date().toISOString(),
        };

        updateBudget(id, updates)
          .then((updatedBudget) => {
            resolve(updatedBudget);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    [updateBudget]
  );

  // Add expense to budget
  const addExpense = useCallback(
    (
      id: string,
      expense: {
        amount: number;
        description: string;
        category?: string;
        date?: string;
        receipt_url?: string;
      }
    ): Promise<Budget> => {
      return new Promise((resolve, reject) => {
        // First fetch current budget to get existing expenses
        fetchBudget(id)
          .then((currentBudget) => {
            if (!currentBudget) {
              throw new Error('Budget not found');
            }

            const currentExpenses = currentBudget.expense_details || [];
            const newExpense = {
              id: crypto.randomUUID(),
              ...expense,
              date: expense.date || new Date().toISOString(),
              created_at: new Date().toISOString(),
            };

            const updatedExpenses = [...currentExpenses, newExpense];
            const totalExpenses = updatedExpenses.reduce(
              (sum, exp) => sum + exp.amount,
              0
            );
            const newCurrentAmount = Math.max(
              0,
              currentBudget.ActualAmount - totalExpenses
            );

            const updates: UpdateBudgetInput = {
              expense_details: updatedExpenses,
              CurrentAmount: newCurrentAmount,
            };

            return updateBudget(id, updates);
          })
          .then((updatedBudget) => {
            resolve(updatedBudget);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    [fetchBudget, updateBudget]
  );

  // Calculate affiliate commission
  const calculateAffiliateCommission = useCallback(
    (budgetAmount: number, commissionRate: number): number => {
      return Math.round(budgetAmount * commissionRate * 100) / 100;
    },
    []
  );

  // Update payout status
  const updatePayoutStatus = useCallback(
    (
      id: string,
      status: 'pending' | 'partially_paid' | 'completed'
    ): Promise<Budget> => {
      return new Promise((resolve, reject) => {
        const updates: UpdateBudgetInput = {
          PayoutStatus: status,
        };

        updateBudget(id, updates)
          .then((updatedBudget) => {
            resolve(updatedBudget);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    [updateBudget]
  );

  // Get budgets by project
  const getBudgetsByProject = useCallback(
    (projectId: string): Budget[] => {
      return budgets.filter((budget) => budget.ProjectId === projectId);
    },
    [budgets]
  );

  // Get budgets by client
  const getBudgetsByClient = useCallback(
    (clientId: string): Budget[] => {
      return budgets.filter((budget) => budget.ClientId === clientId);
    },
    [budgets]
  );

  // Get budgets by affiliate
  const getBudgetsByAffiliate = useCallback(
    (affiliateId: string): Budget[] => {
      return budgets.filter(
        (budget) => budget.has_affiliate && budget.affiliateId === affiliateId
      );
    },
    [budgets]
  );

  // Get budget analytics
  const getBudgetAnalytics = useCallback(() => {
    const totalBudgets = budgets.length;
    const totalAmount = budgets.reduce((sum, b) => sum + b.ActualAmount, 0);
    const totalSpent = budgets.reduce(
      (sum, b) => sum + (b.ActualAmount - b.CurrentAmount),
      0
    );
    const totalRemaining = budgets.reduce((sum, b) => sum + b.CurrentAmount, 0);
    const totalCommissions = budgets
      .filter((b) => b.has_affiliate && b.AffiliateCommission)
      .reduce((sum, b) => sum + (b.AffiliateCommission || 0), 0);

    const statusBreakdown = budgets.reduce(
      (acc, budget) => {
        acc[budget.Status] = (acc[budget.Status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const categoryBreakdown = budgets.reduce(
      (acc, budget) => {
        acc[budget.Category] =
          (acc[budget.Category] || 0) + budget.ActualAmount;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      totalBudgets,
      totalAmount,
      totalSpent,
      totalRemaining,
      totalCommissions,
      spentPercentage: totalAmount > 0 ? (totalSpent / totalAmount) * 100 : 0,
      statusBreakdown,
      categoryBreakdown,
    };
  }, [budgets]);

  return {
    budgets,
    loading,
    error,
    refetch: fetchBudgets,
    addBudget,
    updateBudget,
    deleteBudget,
    fetchBudget,

    // Enhanced budget management functions
    approveBudget,
    addExpense,
    calculateAffiliateCommission,
    updatePayoutStatus,
    getBudgetsByProject,
    getBudgetsByClient,
    getBudgetsByAffiliate,
    getBudgetAnalytics,
  };
}

// Hook for managing proposal-to-client-budget workflow
export function useProposalWorkflow() {
  const { addClient, fetchClient } = useClients();
  const { addBudget } = useBudgets();
  const { addProject } = useProjects();

  // Process approved proposal into client and budget
  const processApprovedProposal = useCallback(
    async (proposal: {
      id: string;
      client_name: string;
      client_email?: string;
      client_phone?: string;
      client_description?: string;
      affiliate_proposal: any;
      user_id: string;
    }): Promise<{
      client: Client;
      budget?: Budget;
      project?: Project;
    }> => {
      try {
        // Extract proposal details
        const proposalData = proposal.affiliate_proposal;
        const budgetAmount =
          proposalData?.budget || proposalData?.estimated_cost;
        const projectName = proposalData?.project_name || proposalData?.title;
        const projectDescription =
          proposalData?.description || proposalData?.project_description;

        // Step 1: Create or update client
        let client: Client;

        // Check if client already exists by email
        if (proposal.client_email) {
          const existingClient = await fetchClient(proposal.client_email);
          if (existingClient) {
            client = existingClient;
          } else {
            // Create new client
            const clientInput: CreateClientInput = {
              name: proposal.client_name,
              email: proposal.client_email,
              phone: proposal.client_phone,
              description: proposal.client_description,
              created_by: proposal.user_id,
            };
            client = await addClient(clientInput);
          }
        } else {
          // Create client without email
          const clientInput: CreateClientInput = {
            name: proposal.client_name,
            phone: proposal.client_phone,
            description: proposal.client_description,
            created_by: proposal.user_id,
          };
          client = await addClient(clientInput);
        }

        let budget: Budget | undefined;
        let project: Project | undefined;

        // Step 2: Create project if project details are available
        if (projectName) {
          const projectInput: CreateProjectInput = {
            name: projectName,
            description: projectDescription,
            is_proposed: true,
            affiliate_user: proposal.user_id,
            client_id: client.id,
          };
          project = await addProject(projectInput);

          // Step 3: Create budget if budget amount is available
          if (budgetAmount && budgetAmount > 0) {
            const budgetInput: CreateBudgetInput = {
              ProjectId: project.id,
              ClientId: client.id,
              ActualAmount: budgetAmount,
              CurrentAmount: budgetAmount,
              Currency: proposalData?.currency || 'USD',
              Category: proposalData?.category || 'development',
              StartDate:
                proposalData?.start_date ||
                new Date().toISOString().split('T')[0],
              EndDate:
                proposalData?.end_date ||
                new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
                  .toISOString()
                  .split('T')[0],
              has_affiliate: true,
              affiliateId: proposal.user_id,
              AffiliateCommission:
                proposalData?.commission || budgetAmount * 0.1, // Default 10% commission
              Notes: `Created from proposal ${proposal.id}`,
            };
            budget = await addBudget(budgetInput);
          }
        }

        return { client, budget, project };
      } catch (error) {
        console.error('Error processing approved proposal:', error);
        throw error;
      }
    },
    [addClient, fetchClient, addBudget, addProject]
  );

  // Extract client information from proposal
  const extractClientFromProposal = useCallback((proposalData: any) => {
    return {
      name:
        proposalData?.client_name ||
        proposalData?.company_name ||
        'Unknown Client',
      email: proposalData?.client_email || proposalData?.contact_email,
      phone: proposalData?.client_phone || proposalData?.contact_phone,
      company_name: proposalData?.company_name,
      description:
        proposalData?.client_description || proposalData?.project_description,
    };
  }, []);

  // Extract budget information from proposal
  const extractBudgetFromProposal = useCallback((proposalData: any) => {
    const budget = proposalData?.budget || proposalData?.estimated_cost;
    if (!budget || budget <= 0) return null;

    return {
      amount: budget,
      currency: proposalData?.currency || 'USD',
      category: proposalData?.category || 'development',
      commission: proposalData?.commission || budget * 0.1,
      start_date: proposalData?.start_date,
      end_date: proposalData?.end_date,
      timeline: proposalData?.timeline,
    };
  }, []);

  // Validate proposal data for processing
  const validateProposalData = useCallback(
    (
      proposalData: any
    ): {
      isValid: boolean;
      errors: string[];
      warnings: string[];
    } => {
      const errors: string[] = [];
      const warnings: string[] = [];

      // Required fields
      if (!proposalData?.client_name && !proposalData?.company_name) {
        errors.push('Client name or company name is required');
      }

      // Budget validation
      const budget = proposalData?.budget || proposalData?.estimated_cost;
      if (budget && budget <= 0) {
        errors.push('Budget amount must be positive');
      }

      // Date validation
      if (proposalData?.start_date && proposalData?.end_date) {
        const startDate = new Date(proposalData.start_date);
        const endDate = new Date(proposalData.end_date);
        if (endDate <= startDate) {
          errors.push('End date must be after start date');
        }
      }

      // Warnings
      if (!proposalData?.client_email && !proposalData?.contact_email) {
        warnings.push(
          'No client email provided - client communication may be limited'
        );
      }

      if (!budget) {
        warnings.push(
          'No budget information provided - budget tracking will not be available'
        );
      }

      if (!proposalData?.project_name && !proposalData?.title) {
        warnings.push(
          'No project name provided - project will need to be created manually'
        );
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
      };
    },
    []
  );

  return {
    processApprovedProposal,
    extractClientFromProposal,
    extractBudgetFromProposal,
    validateProposalData,
  };
}
