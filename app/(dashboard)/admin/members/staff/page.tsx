'use client';

import { useMemo } from 'react';
import { MembersMetrics } from '@/components/members/members-metrics';
import { MembersTable } from '@/components/tables/members/members-table';
import { useMembers } from '@/hooks/use-db';

export default function Page() {
  const { members, loading } = useMembers();

  // Filter for staff members (Admin and Collaborator roles)
  const staffMembers = useMemo(() => {
    return members.filter(
      (member) => member.role === 'Admin' || member.role === 'Collaborator'
    );
  }, [members]);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Staff Members</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <MembersMetrics members={staffMembers} loading={loading} />
        <MembersTable filteredMembers={staffMembers} />
      </section>
    </main>
  );
}
