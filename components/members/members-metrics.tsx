'use client';

import { useMemo } from 'react';
import {
  TrendingDown,
  TrendingUp,
  Users,
  UserCheck,
  UserPlus,
  Shield,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Member } from '@/lib/supabase/database-modules';

interface MembersMetricsProps {
  members: Member[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  variant = 'default',
  icon,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up')
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    if (trend === 'down')
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    return null;
  };

  return (
    <Card className={`${getVariantStyles()} transition-all`}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        {icon && <div className='text-muted-foreground'>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='flex items-center justify-between'>
          <div>
            <div className='text-2xl font-bold'>{value}</div>
            <div className='flex items-center gap-1 text-xs text-muted-foreground'>
              {getTrendIcon()}
              <span>{subtitle}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function MembersMetrics({
  members,
  loading,
}: MembersMetricsProps) {
  const metrics = useMemo(() => {
    if (!members.length) {
      return {
        total: 0,
        active: 0,
        newMembers: 0,
        admins: 0,
      };
    }

    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const total = members.length;
    const active = members.filter(
      (member) => member.status === 'online' || member.status === 'away'
    ).length;
    const newMembers = members.filter((member) => {
      const joinedDate = member.joined_date || member.created_at;
      return joinedDate && new Date(joinedDate) >= lastWeek;
    }).length;
    const admins = members.filter(
      (member) => member.role === 'Admin'
    ).length;

    return {
      total,
      active,
      newMembers,
      admins,
    };
  }, [members]);

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['total', 'active', 'new', 'admins'].map((metric) => (
          <Card key={metric} className='animate-pulse'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <div className='h-4 w-20 bg-muted'></div>
              <div className='h-4 w-4 bg-muted'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-muted  mb-2'></div>
              <div className='h-3 w-24 bg-muted '></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const activePercentage =
    metrics.total > 0 ? Math.round((metrics.active / metrics.total) * 100) : 0;
  const adminPercentage =
    metrics.total > 0 ? Math.round((metrics.admins / metrics.total) * 100) : 0;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='Total Members'
        value={metrics.total}
        subtitle={`${metrics.newMembers} new this week`}
        trend={metrics.newMembers > 0 ? 'up' : 'neutral'}
        icon={<Users className='h-4 w-4' />}
      />

      <MetricCard
        title='Active Members'
        value={metrics.active}
        subtitle={`${activePercentage}% currently active`}
        variant={metrics.active > 0 ? 'success' : 'default'}
        icon={<UserCheck className='h-4 w-4' />}
      />

      <MetricCard
        title='New Members'
        value={metrics.newMembers}
        subtitle='Joined this week'
        variant={metrics.newMembers > 0 ? 'success' : 'default'}
        trend={metrics.newMembers > 0 ? 'up' : 'neutral'}
        icon={<UserPlus className='h-4 w-4' />}
      />

      <MetricCard
        title='Administrators'
        value={metrics.admins}
        subtitle={`${adminPercentage}% admin roles`}
        variant={metrics.admins > 0 ? 'warning' : 'default'}
        icon={<Shield className='h-4 w-4' />}
      />
    </div>
  );
}
