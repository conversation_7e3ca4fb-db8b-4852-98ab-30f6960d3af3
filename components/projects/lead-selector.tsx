'use client';

import { useEffect, useId, useState } from 'react';
import { CheckIcon } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { supabaseClient } from '@/lib/supabase/auth/client';

interface UserOption {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
}

interface LeadSelectorProps {
  lead: UserOption | null;
  onLeadChange?: (userId: string) => void;
}

export function LeadSelector({ lead, onLeadChange }: LeadSelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(lead?.id || '');
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setValue(lead?.id || '');
  }, [lead?.id]);

  // Load user options from Supabase profiles table (collaborators only)
  useEffect(() => {
    const loadUserOptions = async () => {
      try {
        setLoading(true);

        const { data, error } = await supabaseClient
          .from('profiles')
          .select('id, full_name, email, avatar_url')
          .eq('role', 'Collaborator')
          .order('full_name');

        if (error) {
          console.error('Failed to load user options:', error);
        } else {
          const users =
            data?.map((profile) => ({
              id: profile.id,
              name: profile.full_name || profile.email,
              email: profile.email,
              avatar_url: profile.avatar_url,
            })) || [];
          setUserOptions(users);
        }
      } catch (error) {
        console.error('Failed to load user options:', error);
      } finally {
        setLoading(false);
      }
    };

    if (open) {
      loadUserOptions();
    }
  }, [open]);

  const handleLeadChange = (userId: string) => {
    setValue(userId);
    setOpen(false);

    if (onLeadChange) {
      onLeadChange(userId);
    }
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center gap-1 h-7 px-2'
            size='sm'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {lead ? (
              <>
                <Avatar className='size-5 mr-1'>
                  <AvatarImage
                    src={lead.avatar_url || undefined}
                    alt={lead.name}
                  />
                  <AvatarFallback>{lead.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className='text-xs hidden md:inline'>{lead.name}</span>
              </>
            ) : (
              <span className='text-xs'>Select lead</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='border-input w-48 p-0' align='start'>
          <Command>
            <CommandInput placeholder='Set lead...' />
            <CommandList>
              <CommandEmpty>No user found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading users...</span>
                  </CommandItem>
                ) : (
                  userOptions.map((user) => (
                    <CommandItem
                      key={user.id}
                      value={user.id}
                      onSelect={handleLeadChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <Avatar className='size-5'>
                          <AvatarImage
                            src={user.avatar_url || undefined}
                            alt={user.name}
                          />
                          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span className='text-xs'>{user.name}</span>
                      </div>
                      {(value === user.id || lead?.id === user.id) && (
                        <CheckIcon size={14} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
