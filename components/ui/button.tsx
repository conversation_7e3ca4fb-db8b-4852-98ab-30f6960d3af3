import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import type * as React from 'react';
import { cn } from '@/lib/utils/cn';

const buttonVariants = cva(
  'inline-flex cursor-pointer items-center justify-center whitespace-nowrap font-["Cha<PERSON>_Petch"] font-semibold ring-offset-white transition-colors duration-300 ease-linear focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-black-alt dark:focus-visible:ring-accent-300',
  {
    variants: {
      variant: {
        brand:
          'bg-accent-500 text-white hover:bg-accent-400 active:bg-accent-600 dark:bg-accent-500 dark:hover:bg-accent-400 dark:active:bg-accent-600',
        neutral:
          'bg-neutral-700 text-neutral-50 hover:bg-neutral-600 active:bg-neutral-800 dark:bg-neutral-600 dark:hover:bg-neutral-500 dark:active:bg-neutral-700',
        'neutral-inverse':
          'bg-transparent border border-neutral-300 text-neutral-600 hover:bg-neutral-100 hover:border-neutral-400 active:bg-neutral-200 active:border-neutral-500 dark:border-neutral-600 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:border-neutral-500 dark:active:bg-neutral-600 dark:active:border-neutral-400',
        'outline-brand':
          'bg-neutral-50 border border-accent-500 text-accent-500 hover:bg-neutral-100 hover:border-accent-400 active:bg-neutral-100 active:border-accent-600 dark:bg-neutral-900 dark:border-accent-500 dark:text-accent-500 dark:hover:bg-neutral-800 dark:hover:border-accent-400 dark:active:bg-neutral-800 dark:active:border-accent-600',
        'outline-neutral':
          'bg-neutral-50 border border-neutral-700 text-neutral-700 hover:bg-neutral-100 hover:border-neutral-600 active:bg-neutral-200 active:border-neutral-800 dark:bg-neutral-900 dark:border-neutral-600 dark:text-neutral-600 dark:hover:bg-neutral-800 dark:hover:border-neutral-500 dark:active:bg-neutral-800 dark:active:border-neutral-700',
        main: 'w-full bg-accent-100 px-10 py-6 font-bold text-white text-xl capitalize hover:bg-accent-200 active:bg-accent-300 dark:bg-accent-500 dark:hover:bg-accent-200 dark:active:bg-accent-300',
        main_sm:
          'bg-accent-100 px-6 py-3 font-bold text-white text-xl capitalize hover:bg-black-alt active:bg-black-alt dark:bg-accent-500 dark:hover:bg-black-alt dark:active:bg-black-alt',
        main_no_style:
          'bg-accent-100 p-6 font-bold text-white text-xl capitalize hover:bg-black-alt active:bg-black-alt dark:bg-accent-500 dark:hover:bg-black-alt dark:active:bg-black-alt',
        mainsm:
          'bg-accent-100 px-8 py-2 text-sm text-white capitalize hover:bg-black-alt active:bg-black-alt dark:bg-accent-500 dark:hover:bg-black-alt dark:active:bg-black-alt',
        default:
          'bg-black-alt/80 text-white hover:bg-black-alt active:bg-black-alt/90 dark:bg-accent-300 dark:hover:bg-accent-200 dark:active:bg-accent-400',
        shadow:
          'text-neutral-500 shadow-light hover:text-black-alt active:text-black-alt dark:text-muted-foreground dark:shadow-dark dark:hover:text-foreground dark:active:text-foreground',
        shadow_dark:
          'text-white shadow-dark hover:bg-accent-300 active:bg-accent-400 dark:text-foreground dark:shadow-dark dark:hover:bg-accent-300 dark:active:bg-accent-400',
        destructive:
          'bg-destructive text-neutral-50 hover:bg-destructive/90 active:bg-destructive/80 dark:bg-destructive dark:hover:bg-destructive/90 dark:active:bg-destructive/80',
        outline:
          'border border-neutral-300 bg-white hover:bg-muted active:bg-muted/90 dark:border-border dark:bg-background dark:hover:bg-muted dark:active:bg-muted/90',
        outlineTransparent:
          'border border-neutral-200 bg-transparent shadow-2xs hover:bg-muted/40 active:bg-muted/50 dark:border-border dark:hover:bg-muted/40 dark:active:bg-muted/50',
        secondary:
          'bg-secondary text-neutral-900 hover:bg-secondary/80 active:bg-secondary/70 dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-secondary/80 dark:active:bg-secondary/70',
        ghost:
          'hover:text-accent-foreground active:text-accent-foreground dark:text-foreground dark:hover:text-accent-foreground dark:active:text-accent-foreground',
        link: 'text-neutral-900 underline-offset-4 hover:underline active:underline dark:text-foreground dark:hover:underline dark:active:underline',
      },
      size: {
        primary: 'w-28 h-14 p-6 text-base',
        default: 'h-10 px-4 py-2 text-sm',
        sm: 'h-6 px-2 py-1 text-[10px]',
        lg: 'h-11 px-8 text-base rounded-md',
        icon_s: 'size-6 p-1',
        icon_m: 'size-8 p-1',
        icon_l: 'size-14 p-6',
        ghost: 'h-10 px-2',
        none: '',
      },
      iconOnly: {
        true: '',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      iconOnly: false,
    },
  }
);

interface ButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  iconSize?: 'S' | 'M' | 'L';
}

function Button({
  className,
  variant,
  size,
  iconOnly,
  iconSize,
  asChild = false,
  children,
  ...props
}: ButtonProps) {
  const Comp = asChild ? Slot : 'button';

  const renderIcon = () => {
    if (!iconOnly || !iconSize) return null;
    const sizes = { S: 'size-3', M: 'size-4', L: 'size-8' };
    const innerSizes = { S: 'size-2.5', M: 'size-3.5', L: 'size-7' };
    const borderWidth = iconSize === 'L' ? 'border-2' : 'border-[1.5px]';
    const borderColor = variant?.includes('brand')
      ? 'border-white'
      : variant?.includes('neutral-inverse')
        ? 'border-neutral-600'
        : variant?.includes('outline-brand')
          ? 'border-accent-500'
          : 'border-neutral-700';

    return (
      <div className='h-6 flex justify-center items-center'>
        <div
          data-size={iconSize}
          data-type='Neutral'
          className={cn('relative overflow-hidden', sizes[iconSize])}
        >
          <div
            className={cn(
              'absolute left-[1px] top-[1px]',
              innerSizes[iconSize],
              borderWidth,
              borderColor
            )}
          />
        </div>
      </div>
    );
  };

  return (
    <Comp
      data-slot='button'
      className={cn(
        buttonVariants({ variant, size, className }),
        iconOnly && 'p-0'
      )}
      {...props}
    >
      {iconOnly ? renderIcon() : children}
    </Comp>
  );
}

export { Button, buttonVariants };

// import { Slot } from '@radix-ui/react-slot';
// import { cva, type VariantProps } from 'class-variance-authority';
// import type * as React from 'react';

// import { cn } from '@/lib/utils/cn';

// const buttonVariants = cva(
//   'inline-flex cursor-pointer items-center justify-center whitespace-nowrap font-medium text-sm ring-offset-white transition-colors duration-300 ease-linear focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-neutral-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-black-alt dark:focus-visible:ring-branddark-300',
//   {
//     variants: {
//       variant: {
//         main: 'w-full bg-accent-100 px-10 py-6 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-accent-200 hover:text-white dark:bg-branddark-500 dark:hover:bg-branddark-200 dark:hover:text-white',
//         main_sm:
//           'bg-accent-100 px-6 py-3 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
//         main_no_style:
//           'bg-accent-100 p-6 font-bold text-white text-xl capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
//         mainsm:
//           'bg-accent-100 px-8 py-2 text-sm text-white capitalize transition-all duration-300 ease-linear hover:bg-black-alt hover:text-white dark:bg-branddark-500 dark:hover:bg-black-alt dark:hover:text-white',
//         default:
//           'bg-black-alt/80 text-white hover:bg-black-alt dark:bg-branddark-300 dark:hover:bg-branddark-200',
//         shadow:
//           'text-neutral-500 shadow-light transition-all duration-300 ease-linear hover:text-black-alt dark:text-muted-foreground dark:shadow-dark dark:hover:text-foreground',
//         shadow_dark:
//           'text-white shadow-dark transition-all duration-300 ease-linear dark:text-foreground dark:shadow-dark dark:hover:bg-branddark-300 dark:hover:text-foreground',
//         destructive:
//           'bg-destructive text-neutral-50 hover:bg-destructive/90 dark:bg-destructive dark:hover:bg-destructive/90',
//         outline:
//           'border border-neutral-300 bg-white hover:bg-muted hover:text-accent-foreground dark:border-border dark:bg-background dark:hover:bg-muted dark:hover:text-accent-foreground',
//         outlineTransparent:
//           'border border-neutral-200 bg-transparent shadow-2xs hover:bg-muted/40 hover:text-accent-foreground dark:border-border dark:hover:bg-muted/40 dark:hover:text-accent-foreground',
//         secondary:
//           'bg-secondary text-neutral-900 hover:bg-secondary/80 dark:bg-secondary dark:text-secondary-foreground dark:hover:bg-secondary/80',
//         ghost:
//           'hover:text-accent-foreground dark:text-foreground dark:hover:text-accent-foreground',
//         link: 'text-neutral-900 underline-offset-4 hover:underline dark:text-foreground dark:hover:underline',
//         primary: 'w-28 h-14 p-6',
//       },
//       size: {
//         primary: 'w-28 h-14 p-6',
//         default: 'h-10 px-4 py-2',
//         sm: 'h-8 px-4 py-2',
//         lg: 'h-11 rounded-md px-8',
//         icon: 'h-10 w-10',
//         ghost: 'h-10 px-2',
//         none: '',
//       },
//       icon: {
//         none: '',
//         left: 'space-x-2 py-2 pr-4 pl-2',
//         right: 'space-x-2 py-2 pr-2 pl-4',
//         both: 'space-x-2 px-4 py-2',
//       },
//     },
//     defaultVariants: {
//       variant: 'default',
//       size: 'default',
//       icon: 'none',
//     },
//   }
// );

// function Button({
//   className,
//   variant,
//   size,
//   asChild = false,
//   ...props
// }: React.ComponentProps<'button'> &
//   VariantProps<typeof buttonVariants> & {
//     asChild?: boolean;
//   }) {
//   const Comp = asChild ? Slot : 'button';

//   return (
//     <Comp
//       data-slot='button'
//       className={cn(buttonVariants({ variant, size, className }))}
//       {...props}
//     />
//   );
// }

// export { Button, buttonVariants };
