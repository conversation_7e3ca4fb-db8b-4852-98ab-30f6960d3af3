'use client';

import { ArrowLef<PERSON> } from 'lucide-react';
import Link from 'next/link';

import { EditPortfolioForm } from '@/components/affiliate/edit-portfolio-form';
import { But<PERSON> } from '@/components/ui/button';

export default function EditPortfolioPage() {
  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center space-x-4'>
          <Link href='/affiliate/portfolio'>
            <Button variant='ghost' size='sm' className='h-8'>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back
            </Button>
          </Link>
          <p className='font-medium text-sm'>Edit Portfolio</p>
        </div>
      </header>
      <section className='flex-1 p-6'>
        <EditPortfolioForm />
      </section>
    </main>
  );
}
