'use client';

import { 
  Building, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  User, 
  DollarSign,
  FolderOpen,
  Edit,
  Archive,
  ArchiveRestore
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ClientForm } from './client-form';
import { BudgetList } from '@/components/budgets/budget-list';
import { useClients, useProjects, useBudgets, useProposals } from '@/hooks/use-db';
import type { Client } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/format-currency';
import { cn } from '@/lib/utils/cn';

interface ClientProfileProps {
  client: Client;
  className?: string;
}

export function ClientProfile({ client, className }: ClientProfileProps) {
  const { updateClient } = useClients();
  const { projects } = useProjects();
  const { budgets } = useBudgets();
  const { proposals } = useProposals();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Get client-related data
  const clientProjects = projects.filter(p => p.client_id === client.id);
  const clientBudgets = budgets.filter(b => b.ClientId === client.id);
  const clientProposals = proposals.filter(p => p.client_id === client.id);

  // Calculate client statistics
  const totalBudgetAmount = clientBudgets.reduce((sum, budget) => sum + budget.ActualAmount, 0);
  const totalSpentAmount = clientBudgets.reduce((sum, budget) => sum + (budget.ActualAmount - budget.CurrentAmount), 0);
  const activeProjects = clientProjects.filter(p => !p.is_archived).length;
  const completedProjects = clientProjects.filter(p => p.percent_complete === 100).length;

  const handleArchiveToggle = async () => {
    setIsUpdating(true);
    try {
      await updateClient(client.id, { is_active: !client.is_active });
      toast.success(
        client.is_active 
          ? 'Client archived successfully' 
          : 'Client restored successfully'
      );
    } catch (error) {
      console.error('Error updating client status:', error);
      toast.error(
        `Failed to ${client.is_active ? 'archive' : 'restore'} client: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive 
      ? 'bg-green-100 text-green-800' 
      : 'bg-gray-100 text-gray-800';
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Client Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src="" />
                <AvatarFallback className="text-lg">
                  {client.name.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <h1 className="text-2xl font-bold">{client.name}</h1>
                  <Badge className={getStatusColor(client.is_active)}>
                    {client.is_active ? 'Active' : 'Archived'}
                  </Badge>
                </div>
                {client.company_name && (
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Building className="h-4 w-4" />
                    <span>{client.company_name}</span>
                  </div>
                )}
                {client.industry && (
                  <div className="text-sm text-muted-foreground">
                    Industry: {client.industry}
                  </div>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    Edit
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Edit Client</DialogTitle>
                    <DialogDescription>
                      Update client information and contact details.
                    </DialogDescription>
                  </DialogHeader>
                  <ClientForm
                    client={client}
                    onSuccess={() => {
                      setIsEditDialogOpen(false);
                      toast.success('Client updated successfully');
                    }}
                    onCancel={() => setIsEditDialogOpen(false)}
                  />
                </DialogContent>
              </Dialog>
              <Button
                variant={client.is_active ? "destructive" : "default"}
                size="sm"
                onClick={handleArchiveToggle}
                disabled={isUpdating}
                className="flex items-center gap-2"
              >
                {client.is_active ? (
                  <>
                    <Archive className="h-4 w-4" />
                    {isUpdating ? 'Archiving...' : 'Archive'}
                  </>
                ) : (
                  <>
                    <ArchiveRestore className="h-4 w-4" />
                    {isUpdating ? 'Restoring...' : 'Restore'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Contact Information */}
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-muted-foreground">Contact Information</h3>
              <div className="space-y-2">
                {client.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a 
                      href={`mailto:${client.email}`}
                      className="text-sm hover:underline"
                    >
                      {client.email}
                    </a>
                  </div>
                )}
                {client.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <a 
                      href={`tel:${client.phone}`}
                      className="text-sm hover:underline"
                    >
                      {client.phone}
                    </a>
                  </div>
                )}
                {client.contact_person && (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{client.contact_person}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Project Statistics */}
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-muted-foreground">Project Statistics</h3>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <FolderOpen className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{activeProjects} Active Projects</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{completedProjects} Completed</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{clientBudgets.length} Budgets</span>
                </div>
              </div>
            </div>

            {/* Financial Summary */}
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-muted-foreground">Financial Summary</h3>
              <div className="space-y-2">
                <div className="text-sm">
                  <div className="font-medium">Total Budget</div>
                  <div className="text-muted-foreground">
                    {formatCurrency(totalBudgetAmount, 'USD')}
                  </div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">Total Spent</div>
                  <div className="text-muted-foreground">
                    {formatCurrency(totalSpentAmount, 'USD')}
                  </div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">Remaining</div>
                  <div className="text-muted-foreground">
                    {formatCurrency(totalBudgetAmount - totalSpentAmount, 'USD')}
                  </div>
                </div>
              </div>
            </div>

            {/* Account Information */}
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-muted-foreground">Account Information</h3>
              <div className="space-y-2">
                <div className="text-sm">
                  <div className="font-medium">Client Since</div>
                  <div className="text-muted-foreground">
                    {formatDate(client.created_at)}
                  </div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">Last Updated</div>
                  <div className="text-muted-foreground">
                    {formatDate(client.updated_at)}
                  </div>
                </div>
                <div className="text-sm">
                  <div className="font-medium">Proposals</div>
                  <div className="text-muted-foreground">
                    {clientProposals.length} submitted
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          {client.description && (
            <>
              <Separator className="my-6" />
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Description</h3>
                <p className="text-sm">{client.description}</p>
              </div>
            </>
          )}

          {/* Address */}
          {client.address && (
            <>
              <Separator className="my-6" />
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Address
                </h3>
                <div className="text-sm">
                  {typeof client.address === 'string' 
                    ? client.address 
                    : JSON.stringify(client.address, null, 2)
                  }
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="projects" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="projects">Projects ({clientProjects.length})</TabsTrigger>
          <TabsTrigger value="budgets">Budgets ({clientBudgets.length})</TabsTrigger>
          <TabsTrigger value="proposals">Proposals ({clientProposals.length})</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="projects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Client Projects</CardTitle>
              <CardDescription>
                All projects associated with this client
              </CardDescription>
            </CardHeader>
            <CardContent>
              {clientProjects.length > 0 ? (
                <div className="space-y-4">
                  {clientProjects.map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {project.description || 'No description'}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Progress: {project.percent_complete}% • 
                          {project.is_archived ? ' Archived' : ' Active'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {project.start_date && formatDate(project.start_date)}
                        </div>
                        {project.target_date && (
                          <div className="text-xs text-muted-foreground">
                            Due: {formatDate(project.target_date)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No projects found for this client</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="budgets" className="space-y-4">
          <BudgetList clientId={client.id} />
        </TabsContent>

        <TabsContent value="proposals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Client Proposals</CardTitle>
              <CardDescription>
                Proposals submitted for this client
              </CardDescription>
            </CardHeader>
            <CardContent>
              {clientProposals.length > 0 ? (
                <div className="space-y-4">
                  {clientProposals.map((proposal) => (
                    <div key={proposal.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">Proposal #{proposal.id}</div>
                        <div className="text-sm text-muted-foreground">
                          From: {proposal.user_email}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {formatDate(proposal.created_at)}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={
                          proposal.is_approved === true ? 'bg-green-100 text-green-800' :
                          proposal.is_approved === false ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }>
                          {proposal.is_approved === true ? 'Approved' :
                           proposal.is_approved === false ? 'Rejected' :
                           'Pending'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No proposals found for this client</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Timeline of client-related activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Activity timeline coming soon</p>
                <p className="text-sm">This will show project updates, budget changes, and proposal activities</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
