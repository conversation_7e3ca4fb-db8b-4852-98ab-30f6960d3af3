import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { IssuePriorityChange } from '@/lib/emails/issues/priority-changed';
import type { IssuePriorityChangeEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request) {
  return new Promise((resolve, reject) => {
    let emailData: IssuePriorityChangeEmailData;

    request
      .json()
      .then((data: IssuePriorityChangeEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.issue ||
          !emailData.project ||
          !emailData.oldPriority ||
          !emailData.newPriority ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: issue, project, oldPriority, newPriority, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = IssuePriorityChange(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(IssuePriorityChange(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Issue Priority Changed: ${emailData.issue.title}`,
          react: IssuePriorityChange(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Issue priority change email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Issue Priority Changed: ${emailData.issue.title}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Issue priority change email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error:
              error.message || 'Failed to send issue priority change email',
          })
        );
      });
  });
}
