'use client';

import { DataTable } from '@/components/ui/data-table';
import { useTeams } from '@/hooks/use-db';

import { createColumns } from './columns';

export function TeamsTable() {
  const { teams, loading, error } = useTeams();
  const columns = createColumns();

  if (error) {
    return (
      <div className='w-full p-6'>
        <div className='text-center text-red-500'>{error}</div>
      </div>
    );
  }

  return <DataTable columns={columns} data={teams} isLoading={loading} />;
}
