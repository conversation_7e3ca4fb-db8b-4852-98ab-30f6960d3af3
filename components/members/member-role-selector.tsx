'use client';

import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { Member } from '@/lib/supabase/database-modules';

interface MemberRoleSelectorProps {
  member: Member;
  onRoleChange: (
    memberId: string,
    role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
  ) => Promise<void>;
}

const getRoleBadgeVariant = (role: string) => {
  switch (role) {
    case 'Admin':
      return 'destructive';
    case 'Collaborator':
      return 'default';
    case 'Affiliate':
      return 'secondary';
    case 'Volunteer':
      return 'outline';
    default:
      return 'secondary';
  }
};

const roleOptions = [
  { value: 'Admin', label: 'Admin' },
  { value: 'Collaborator', label: 'Collaborator' },
  { value: 'Affiliate', label: 'Affiliate' },
  { value: 'Volunteer', label: 'Volunteer' },
] as const;

export function MemberRoleSelector({
  member,
  onRoleChange,
}: MemberRoleSelectorProps) {
  const handleRoleChange = (newRole: string) => {
    if (newRole !== member.role) {
      const memberName = member.full_name || member.name;
      const roleChangePromise = onRoleChange(
        member.id,
        newRole as 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
      );

      toast.promise(roleChangePromise, {
        loading: `Updating ${memberName}'s role...`,
        success: `${memberName}'s role updated to ${newRole}`,
        error: (error) =>
          `Failed to update role: ${error?.message || 'Unknown error'}`,
      });
    }
  };

  return (
    <Select value={member.role} onValueChange={handleRoleChange}>
      <SelectTrigger className='w-auto h-auto p-2 border-none bg-transparent hover:bg-muted/50 shadow-none'>
        <SelectValue asChild>
          <Badge
            variant={getRoleBadgeVariant(member.role)}
            className='cursor-pointer'
          >
            {member.role}
          </Badge>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {roleOptions.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            <div className='flex items-center gap-2'>
              <Badge
                variant={getRoleBadgeVariant(option.value)}
                className='text-xs'
              >
                {option.label}
              </Badge>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
