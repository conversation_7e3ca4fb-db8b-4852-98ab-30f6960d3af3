'use client';

import { useState } from 'react';
import { Plus, X, UserPlus } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useMembers, useProjectMembers } from '@/hooks/use-db';
import type { ProjectMember } from '@/lib/supabase/database-modules';

interface ProjectMembersManagerProps {
  projectId: string;
  projectMembers: ProjectMember[];
  onMembersUpdate: () => void;
}

export function ProjectMembersManager({
  projectId,
  projectMembers,
  onMembersUpdate,
}: ProjectMembersManagerProps) {
  const { members } = useMembers();
  const { addProjectMember, updateProjectMember, removeProjectMember } =
    useProjectMembers();
  const [open, setOpen] = useState(false);

  const availableMembers = members.filter(
    (member) =>
      !projectMembers.some((pm) => pm.user_id === member.id) &&
      member.role !== 'Affiliate' // Exclude affiliates from project membership
  );

  const handleAddMember = (
    memberId: string,
    role: 'lead' | 'member' | 'viewer' = 'member'
  ) => {
    // Check if the user is an affiliate
    const selectedMember = members.find((m) => m.id === memberId);
    if (selectedMember?.role === 'Affiliate') {
      toast.error('Affiliates are not allowed to be added as project members');
      return;
    }

    addProjectMember({
      project_id: projectId,
      user_id: memberId,
      role,
    })
      .then(() => {
        toast.success('Member added successfully');
        onMembersUpdate();
        setOpen(false);
      })
      .catch((error) => {
        const errorMessage = error?.message || 'Unknown error occurred';
        toast.error(`Failed to add member: ${errorMessage}`);
        console.error('Error adding member:', error);
      });
  };

  const handleRemoveMember = (userId: string) => {
    removeProjectMember(projectId, userId)
      .then(() => {
        toast.success('Member removed successfully');
        onMembersUpdate();
      })
      .catch((error) => {
        toast.error('Failed to remove member');
        console.error('Error removing member:', error);
      });
  };

  const handleRoleChange = (membershipId: string, newRole: string) => {
    updateProjectMember(membershipId, {
      role: newRole as 'lead' | 'member' | 'viewer',
    })
      .then(() => {
        toast.success('Member role updated successfully');
        onMembersUpdate();
      })
      .catch((error) => {
        toast.error('Failed to update member role');
        console.error('Error updating member role:', error);
      });
  };

  return (
    <div className='space-y-2'>
      {/* Current Members */}
      {projectMembers.length === 0 ? (
        <div className='text-sm text-muted-foreground'>No members assigned</div>
      ) : (
        <div className='space-y-2'>
          {projectMembers.map((member) => (
            <div
              key={member.id}
              className='flex items-center justify-between gap-2'
            >
              <div className='flex items-center gap-2 flex-1 min-w-0'>
                <Avatar className='h-6 w-6'>
                  <AvatarImage src={member.profiles?.avatar_url || ''} />
                  <AvatarFallback className='text-xs'>
                    {(
                      member.profiles?.full_name ||
                      member.profiles?.email ||
                      'U'
                    )
                      .charAt(0)
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className='flex-1 min-w-0'>
                  <div className='text-sm font-medium truncate'>
                    {member.profiles?.full_name || 'Unknown User'}
                  </div>
                  <div className='text-xs text-muted-foreground'>
                    {member.role}
                  </div>
                </div>
              </div>
              <div className='flex items-center gap-1'>
                <Select
                  value={member.role}
                  onValueChange={(value) => handleRoleChange(member.id, value)}
                >
                  <SelectTrigger className='h-6 w-16 text-xs'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent align='end'>
                    <SelectItem value='lead'>Lead</SelectItem>
                    <SelectItem value='member'>Member</SelectItem>
                    <SelectItem value='viewer'>Viewer</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-6 w-6 p-0'
                  onClick={() => handleRemoveMember(member.user_id)}
                >
                  <X className='h-3 w-3' />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Member Button */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            size='sm'
            className='h-8 w-full justify-start'
          >
            <Plus className='h-4 w-4 mr-2' />
            Add Member
          </Button>
        </PopoverTrigger>
        <PopoverContent className='w-80 p-0' align='start'>
          <Command>
            <CommandInput placeholder='Search members...' />
            <CommandList>
              <CommandEmpty>No members found.</CommandEmpty>
              <CommandGroup>
                {availableMembers.map((member) => (
                  <CommandItem
                    key={member.id}
                    onSelect={() => handleAddMember(member.id)}
                    className='flex items-center gap-3 p-3'
                  >
                    <Avatar className='h-8 w-8'>
                      <AvatarImage src={member.avatar_url || ''} />
                      <AvatarFallback>
                        {(member.full_name || member.name || member.email)
                          .charAt(0)
                          .toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className='flex-1 min-w-0'>
                      <div className='text-sm font-medium'>
                        {member.full_name || member.name}
                      </div>
                      <div className='text-xs text-muted-foreground'>
                        {member.email}
                      </div>
                    </div>
                    <UserPlus className='h-4 w-4' />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
