'use client';

import { use, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { ClientProfile } from '@/components/clients/client-profile';
import { useClients } from '@/hooks/use-db';
import type { Client } from '@/lib/supabase/database-modules';

interface ClientDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ClientDetailPage({ params }: ClientDetailPageProps) {
  const router = useRouter();
  const { clients, loading } = useClients();
  const [client, setClient] = useState<Client | null>(null);
  const [notFound, setNotFound] = useState(false);
  
  const resolvedParams = use(params);
  const clientId = resolvedParams.id;

  useEffect(() => {
    if (!loading && clients.length > 0) {
      const foundClient = clients.find(c => c.id === clientId);
      if (foundClient) {
        setClient(foundClient);
      } else {
        setNotFound(true);
      }
    }
  }, [clients, loading, clientId]);

  if (loading) {
    return (
      <main className="h-full flex flex-col">
        <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <p className="font-medium text-sm">Client Details</p>
          </div>
        </header>
        <section className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading client details...
          </div>
        </section>
      </main>
    );
  }

  if (notFound) {
    return (
      <main className="h-full flex flex-col">
        <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <p className="font-medium text-sm">Client Not Found</p>
          </div>
        </header>
        <section className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Client Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The client you're looking for doesn't exist or has been deleted.
            </p>
            <Button onClick={() => router.push('/admin/clients')}>
              Go to Clients
            </Button>
          </div>
        </section>
      </main>
    );
  }

  if (!client) {
    return null;
  }

  return (
    <main className="h-full flex flex-col">
      <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <p className="font-medium text-sm">{client.name}</p>
        </div>
      </header>
      <section className="flex-1 p-6">
        <ClientProfile client={client} />
      </section>
    </main>
  );
}
