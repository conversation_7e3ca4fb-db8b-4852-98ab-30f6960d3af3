'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { ViewApplicationDialog } from '@/components/applications/view-application-dialog';
import { DataTable } from '@/components/ui/data-table';
import { useApplications } from '@/hooks/use-db';
import type { Application } from '@/lib/supabase/database-modules';

import { createColumns } from './columns';

export function ApplicationsTable() {
  const { applications, loading, error, updateApplication } = useApplications();
  const [selectedApplication, setSelectedApplication] =
    useState<Application | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const handleViewApplication = (application: Application) => {
    setSelectedApplication(application);
    setIsViewDialogOpen(true);
  };

  const handleReviewedChange = (
    applicationId: number,
    reviewed: 'reviewed' | 'received' | 'notAccepted'
  ) => {
    const updatePromise = updateApplication(applicationId, { reviewed });

    const statusText =
      reviewed === 'reviewed'
        ? 'reviewed'
        : reviewed === 'received'
          ? 'received'
          : 'not accepted';

    toast.promise(updatePromise, {
      loading: 'Updating review status...',
      success: `Application marked as ${statusText}`,
      error: (error) =>
        `Failed to update review status: ${error?.message || 'Unknown error'}`,
    });
  };

  const handleApprovedChange = (
    applicationId: number,
    approved: 'accepted' | 'reviewing' | 'notAccepted'
  ) => {
    const updatePromise = updateApplication(applicationId, { approved });

    const statusText =
      approved === 'accepted'
        ? 'accepted'
        : approved === 'reviewing'
          ? 'under review'
          : 'not accepted';

    toast.promise(updatePromise, {
      loading: 'Updating approval status...',
      success: `Application ${statusText}`,
      error: (error) =>
        `Failed to update approval status: ${error?.message || 'Unknown error'}`,
    });
  };

  const columns = createColumns({
    onViewApplication: handleViewApplication,
    onReviewedChange: handleReviewedChange,
    onApprovedChange: handleApprovedChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <>
      <DataTable columns={columns} data={applications} isLoading={loading} />

      <ViewApplicationDialog
        application={selectedApplication}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      />
    </>
  );
}
