'use client';

import { format } from 'date-fns';
import { ExternalLink } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import type { Application } from '@/lib/supabase/database-modules';

interface ViewApplicationDialogProps {
  application: Application | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const getReviewedStatusBadge = (
  status: 'reviewed' | 'received' | 'notAccepted'
) => {
  if (status === 'reviewed') {
    return (
      <Badge
        variant='default'
        className='bg-green-100 text-green-800 hover:bg-green-100'
      >
        Reviewed
      </Badge>
    );
  }
  if (status === 'notAccepted') {
    return <Badge variant='destructive'>Not Accepted</Badge>;
  }
  return <Badge variant='secondary'>Received</Badge>;
};

const getApprovedStatusBadge = (
  status: 'accepted' | 'reviewing' | 'notAccepted'
) => {
  if (status === 'accepted') {
    return (
      <Badge
        variant='default'
        className='bg-green-100 text-green-800 hover:bg-green-100'
      >
        Accepted
      </Badge>
    );
  }
  if (status === 'notAccepted') {
    return <Badge variant='destructive'>Not Accepted</Badge>;
  }
  return <Badge variant='secondary'>Reviewing</Badge>;
};

const getRoleBadge = (role: string) => {
  const variant =
    role === 'Admin'
      ? 'destructive'
      : role === 'Collaborator'
        ? 'default'
        : role === 'Affiliate'
          ? 'secondary'
          : 'outline';
  return <Badge variant={variant}>{role}</Badge>;
};

export function ViewApplicationDialog({
  application,
  open,
  onOpenChange,
}: ViewApplicationDialogProps) {
  if (!application) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl max-h-[80vh] p-0'>
        <DialogHeader className='p-4'>
          <DialogTitle>Application Details</DialogTitle>
          <DialogDescription>
            View comprehensive details for this job application
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className='max-h-[60vh] p-4'>
          <div className='space-y-6'>
            {/* Personal Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Personal Information</h3>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Full Name
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.full_name || 'No name provided'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Email Address
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.email}
                  </p>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Phone Number
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.phone || 'No phone provided'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Location
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.location || 'Not provided'}
                  </p>
                </div>
              </div>

              {application.dob && (
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Date of Birth
                  </div>
                  <p className='text-sm font-medium mt-1'>{application.dob}</p>
                </div>
              )}
            </div>

            <Separator />

            {/* Application Details */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Application Details</h3>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Applied Position
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.position === 'Other' &&
                    application.position_other
                      ? application.position_other
                      : application.position || 'Not specified'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Join Role
                  </div>
                  <div className='mt-1'>
                    {getRoleBadge(application.join_role)}
                  </div>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    How did you hear about us?
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.referer || 'Not provided'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Resume/CV
                  </div>
                  <div className='mt-1'>
                    {application.resume_url ? (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() =>
                          window.open(application.resume_url || '', '_blank')
                        }
                      >
                        <ExternalLink className='h-3 w-3 mr-1' />
                        View Resume
                      </Button>
                    ) : (
                      <span className='text-sm text-muted-foreground'>
                        No resume uploaded
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div className='text-sm font-medium text-muted-foreground'>
                  Application Date
                </div>
                <p className='text-sm font-medium mt-1'>
                  {format(
                    new Date(application.created_at),
                    "MMMM dd, yyyy 'at' HH:mm"
                  )}
                </p>
              </div>
            </div>

            <Separator />

            {/* Message */}
            {application.message && (
              <>
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>Application Message</h3>
                  <div className='bg-muted/50 rounded-lg p-4'>
                    <p className='text-sm whitespace-pre-wrap leading-relaxed'>
                      {application.message}
                    </p>
                  </div>
                </div>
                <Separator />
              </>
            )}

            <Separator />

            {/* Volunteer Information */}
            {(application.interests ||
              application.skills ||
              application.areas ||
              application.why_join ||
              application.hours_per_week ||
              application.past_experience ||
              application.available_days ||
              application.preferred_time ||
              application.equipment ||
              application.additional_info) && (
              <>
                <div className='space-y-4'>
                  <h3 className='text-lg font-semibold'>
                    Volunteer Information
                  </h3>

                  {application.interests && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Interests
                      </div>
                      <p className='text-sm mt-1'>{application.interests}</p>
                    </div>
                  )}

                  {application.skills && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Skills
                      </div>
                      <p className='text-sm mt-1'>{application.skills}</p>
                    </div>
                  )}

                  {application.areas && application.areas.length > 0 && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Areas of Interest
                      </div>
                      <div className='flex flex-wrap gap-1 mt-1'>
                        {application.areas.map((area) => (
                          <Badge
                            key={area}
                            variant='outline'
                            className='text-xs'
                          >
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {application.other_area && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Other Area
                      </div>
                      <p className='text-sm mt-1'>{application.other_area}</p>
                    </div>
                  )}

                  {application.why_join && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Why do you want to join?
                      </div>
                      <div className='bg-muted/50 rounded-lg p-3 mt-1'>
                        <p className='text-sm whitespace-pre-wrap'>
                          {application.why_join}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className='grid grid-cols-2 gap-4'>
                    {application.hours_per_week && (
                      <div>
                        <div className='text-sm font-medium text-muted-foreground'>
                          Hours per Week
                        </div>
                        <p className='text-sm mt-1'>
                          {application.hours_per_week}
                        </p>
                      </div>
                    )}

                    {application.preferred_time && (
                      <div>
                        <div className='text-sm font-medium text-muted-foreground'>
                          Preferred Time
                        </div>
                        <p className='text-sm mt-1'>
                          {application.preferred_time}
                        </p>
                      </div>
                    )}
                  </div>

                  {application.available_days &&
                    application.available_days.length > 0 && (
                      <div>
                        <div className='text-sm font-medium text-muted-foreground'>
                          Available Days
                        </div>
                        <div className='flex flex-wrap gap-1 mt-1'>
                          {application.available_days.map((day) => (
                            <Badge
                              key={day}
                              variant='outline'
                              className='text-xs'
                            >
                              {day}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                  {application.past_experience && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Past Experience
                      </div>
                      <div className='bg-muted/50 rounded-lg p-3 mt-1'>
                        <p className='text-sm whitespace-pre-wrap'>
                          {application.past_experience}
                        </p>
                      </div>
                    </div>
                  )}

                  {application.equipment && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Equipment Access
                      </div>
                      <p className='text-sm mt-1'>{application.equipment}</p>
                    </div>
                  )}

                  {application.additional_info && (
                    <div>
                      <div className='text-sm font-medium text-muted-foreground'>
                        Additional Information
                      </div>
                      <div className='bg-muted/50 rounded-lg p-3 mt-1'>
                        <p className='text-sm whitespace-pre-wrap'>
                          {application.additional_info}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                <Separator />
              </>
            )}

            {/* Status Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Status Information</h3>

              <div className='grid grid-cols-3 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Reviewed Status
                  </div>
                  <div className='mt-1'>
                    {getReviewedStatusBadge(application.reviewed)}
                  </div>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Approval Status
                  </div>
                  <div className='mt-1'>
                    {getApprovedStatusBadge(application.approved)}
                  </div>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Newsletter Subscription
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.newsletter ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    NDA Signed
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {application.is_nda_signed ? 'Yes' : 'No'}
                  </p>
                </div>

                {application.nda_signed_date && (
                  <div>
                    <div className='text-sm font-medium text-muted-foreground'>
                      NDA Signed Date
                    </div>
                    <p className='text-sm font-medium mt-1'>
                      {application.nda_signed_date}
                    </p>
                  </div>
                )}
              </div>

              <div>
                <div className='text-sm font-medium text-muted-foreground'>
                  Volunteer Form Submitted
                </div>
                <p className='text-sm font-medium mt-1'>
                  {application.is_vol_form_submited ? 'Yes' : 'No'}
                </p>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
