'use client';

import { Plus } from 'lucide-react';
import Link from 'next/link';

import { ReferralsMetrics } from '@/components/affiliate/referrals-metrics';
import { ReferralsTable } from '@/components/tables/affiliate/referrals-table';
import { Button } from '@/components/ui/button';
import { useProposals } from '@/hooks/use-db';

export default function ReferralsPage() {
  const { proposals, loading } = useProposals(true); // Use affiliate filtering

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Referrals</p>
        <Link href='/affiliate/referrals/new'>
          <Button size='sm' className='h-8'>
            <Plus className='h-4 w-4 mr-2' />
            New Referral
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <ReferralsMetrics proposals={proposals} loading={loading} />
        <ReferralsTable />
      </section>
    </main>
  );
}
