import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProjectMemberAdded } from '@/lib/emails/projects/member-added';
import type { ProjectMemberAddedEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request) {
  const emailData: ProjectMemberAddedEmailData = await request.json();
  console.log('📧 Sending project member added email');

  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: emailData.recipients,
      bcc: ['<EMAIL>'],
      subject: `You've been added to ${emailData.project.name}`,
      text: '',
      react: ProjectMemberAdded(emailData),
    });

    console.log(
      '✅ Project member added email sent successfully:',
      data.data?.id
    );
    return NextResponse.json({ data });
  } catch (error) {
    console.error('❌ Project member added email error:', error);
    return NextResponse.json({ error });
  }
}
