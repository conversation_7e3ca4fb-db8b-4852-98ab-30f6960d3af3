# Database Schema Analysis for Budget Management System

## Current Schema Status

### ✅ Existing Tables (Properly Configured)

#### 1. **budgets** Table
```sql
-- Current structure based on database-types.ts
CREATE TABLE budgets (
  id TEXT PRIMARY KEY,
  ProjectId TEXT NOT NULL,
  ClientId TEXT,
  ActualAmount NUMERIC NOT NULL,
  CurrentAmount NUMERIC NOT NULL,
  Currency TEXT DEFAULT 'USD',
  Category TEXT DEFAULT 'development',
  Status TEXT DEFAULT 'draft',
  has_affiliate BOOLEAN DEFAULT false,
  affiliateId TEXT,
  AffiliateCommission NUMERIC,
  has_collaborator B<PERSON>OLEAN DEFAULT false,
  collaborators JSONB,
  expense_details JSONB,
  PayoutStatus TEXT,
  ApprovedBy TEXT,
  ApprovalDate TIMESTAMPTZ,
  StartDate DATE NOT NULL,
  EndDate DATE NOT NULL,
  Notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Foreign Key Relationships:**
- `ProjectId` → `projects(id)`
- `ClientId` → `clients(id)`
- `affiliateId` → `profiles(id)`
- `ApprovedBy` → `profiles(id)`

#### 2. **clients** Table
```sql
CREATE TABLE clients (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  description TEXT,
  company_name TEXT,
  industry TEXT,
  contact_person TEXT,
  address JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by TEXT,
  is_active BOOLEAN DEFAULT true
);
```

**Foreign Key Relationships:**
- `created_by` → `profiles(id)`

#### 3. **projects** Table (Enhanced)
```sql
-- Current structure includes budget integration
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  percent_complete INTEGER DEFAULT 0 CHECK (percent_complete >= 0 AND percent_complete <= 100),
  start_date DATE,
  target_date DATE,
  due_date DATE,
  completed_date DATE,
  lead_id UUID,
  status_id TEXT,
  priority_id TEXT,
  health_id TEXT CHECK (health_id IN ('no-update', 'off-track', 'on-track', 'at-risk')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID,
  is_archived BOOLEAN DEFAULT false,
  
  -- Budget and Client Integration
  budget NUMERIC,
  budget_id TEXT,
  client_id TEXT,
  
  -- Affiliate Integration
  is_proposed BOOLEAN,
  affiliate_user TEXT,
  members TEXT[]
);
```

**Foreign Key Relationships:**
- `lead_id` → `profiles(id)`
- `created_by` → `profiles(id)`
- `budget_id` → `budgets(id)`
- `client_id` → `clients(id)`
- `affiliate_user` → `profiles(id)`
- `status_id` → `status(id)`
- `priority_id` → `priorities(id)`
- `health_id` → `health_status(id)`

#### 4. **affiliate_proposals** Table
```sql
CREATE TABLE affiliate_proposals (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  user_id TEXT,
  user_email TEXT,
  affiliate_proposal JSONB,
  is_recieved BOOLEAN,
  is_approved BOOLEAN,
  completed BOOLEAN,
  
  -- Client Information (for proposal-to-client workflow)
  client_name TEXT,
  client_email TEXT,
  client_phone TEXT,
  client_description TEXT,
  client_id TEXT
);
```

**Foreign Key Relationships:**
- `user_id` → `profiles(id)`
- `client_id` → `clients(id)`

### ⚠️ Missing Schema Elements

#### 1. **Missing Constraints and Enums**
```sql
-- Add enum types for better data integrity
CREATE TYPE budget_status AS ENUM ('draft', 'approved', 'locked', 'spent');
CREATE TYPE budget_category AS ENUM ('marketing', 'development', 'consulting', 'operations', 'other');
CREATE TYPE currency_type AS ENUM ('USD', 'EUR', 'GBP', 'JPY');
CREATE TYPE payout_status AS ENUM ('pending', 'partially_paid', 'completed');

-- Update budgets table to use enums
ALTER TABLE budgets 
  ALTER COLUMN Status TYPE budget_status USING Status::budget_status,
  ALTER COLUMN Category TYPE budget_category USING Category::budget_category,
  ALTER COLUMN Currency TYPE currency_type USING Currency::currency_type,
  ALTER COLUMN PayoutStatus TYPE payout_status USING PayoutStatus::payout_status;
```

#### 2. **Missing Indexes for Performance**
```sql
-- Budget table indexes
CREATE INDEX idx_budgets_project_id ON budgets(ProjectId);
CREATE INDEX idx_budgets_client_id ON budgets(ClientId);
CREATE INDEX idx_budgets_affiliate_id ON budgets(affiliateId);
CREATE INDEX idx_budgets_status ON budgets(Status);
CREATE INDEX idx_budgets_category ON budgets(Category);
CREATE INDEX idx_budgets_start_date ON budgets(StartDate);
CREATE INDEX idx_budgets_end_date ON budgets(EndDate);
CREATE INDEX idx_budgets_approved_by ON budgets(ApprovedBy);

-- Client table indexes
CREATE INDEX idx_clients_created_by ON clients(created_by);
CREATE INDEX idx_clients_is_active ON clients(is_active);
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_clients_company_name ON clients(company_name);

-- Project budget integration indexes
CREATE INDEX idx_projects_budget_id ON projects(budget_id);
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_affiliate_user ON projects(affiliate_user);
CREATE INDEX idx_projects_is_proposed ON projects(is_proposed);

-- Proposal table indexes
CREATE INDEX idx_proposals_user_id ON affiliate_proposals(user_id);
CREATE INDEX idx_proposals_client_id ON affiliate_proposals(client_id);
CREATE INDEX idx_proposals_is_approved ON affiliate_proposals(is_approved);
CREATE INDEX idx_proposals_completed ON affiliate_proposals(completed);
```

#### 3. **Missing Foreign Key Constraints**
```sql
-- Add explicit foreign key constraints
ALTER TABLE budgets 
  ADD CONSTRAINT fk_budgets_project FOREIGN KEY (ProjectId) REFERENCES projects(id) ON DELETE CASCADE,
  ADD CONSTRAINT fk_budgets_client FOREIGN KEY (ClientId) REFERENCES clients(id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_budgets_affiliate FOREIGN KEY (affiliateId) REFERENCES profiles(id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_budgets_approved_by FOREIGN KEY (ApprovedBy) REFERENCES profiles(id) ON DELETE SET NULL;

ALTER TABLE projects
  ADD CONSTRAINT fk_projects_budget FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_projects_client FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_projects_affiliate_user FOREIGN KEY (affiliate_user) REFERENCES profiles(id) ON DELETE SET NULL;

ALTER TABLE clients
  ADD CONSTRAINT fk_clients_created_by FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;

ALTER TABLE affiliate_proposals
  ADD CONSTRAINT fk_proposals_user FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL,
  ADD CONSTRAINT fk_proposals_client FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL;
```

#### 4. **Missing Check Constraints**
```sql
-- Budget validation constraints
ALTER TABLE budgets 
  ADD CONSTRAINT chk_budgets_amounts CHECK (ActualAmount > 0 AND CurrentAmount >= 0),
  ADD CONSTRAINT chk_budgets_current_not_exceed_actual CHECK (CurrentAmount <= ActualAmount),
  ADD CONSTRAINT chk_budgets_dates CHECK (EndDate > StartDate),
  ADD CONSTRAINT chk_budgets_commission CHECK (
    (has_affiliate = false AND AffiliateCommission IS NULL) OR 
    (has_affiliate = true AND AffiliateCommission > 0)
  );

-- Client validation constraints
ALTER TABLE clients
  ADD CONSTRAINT chk_clients_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL);
```

### 🔧 Required Database Functions and Triggers

#### 1. **Automatic Timestamp Updates**
```sql
-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all relevant tables
CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON budgets 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 2. **Budget Validation Function**
```sql
-- Function to validate budget operations
CREATE OR REPLACE FUNCTION validate_budget_operation()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent modification of locked or spent budgets
    IF OLD.Status IN ('locked', 'spent') AND NEW.Status != OLD.Status THEN
        RAISE EXCEPTION 'Cannot modify budget with status: %', OLD.Status;
    END IF;
    
    -- Validate affiliate commission
    IF NEW.has_affiliate = true AND NEW.AffiliateCommission IS NULL THEN
        RAISE EXCEPTION 'Affiliate commission required when has_affiliate is true';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER validate_budget_before_update 
  BEFORE UPDATE ON budgets 
  FOR EACH ROW EXECUTE FUNCTION validate_budget_operation();
```

### 📊 Recommended Database Views

#### 1. **Budget Summary View**
```sql
CREATE VIEW budget_summary AS
SELECT 
  b.*,
  p.name as project_name,
  c.name as client_name,
  c.company_name as client_company,
  affiliate.full_name as affiliate_name,
  approver.full_name as approved_by_name,
  (b.ActualAmount - b.CurrentAmount) as spent_amount,
  ROUND(((b.ActualAmount - b.CurrentAmount) / b.ActualAmount * 100), 2) as spent_percentage
FROM budgets b
LEFT JOIN projects p ON b.ProjectId = p.id
LEFT JOIN clients c ON b.ClientId = c.id
LEFT JOIN profiles affiliate ON b.affiliateId = affiliate.id
LEFT JOIN profiles approver ON b.ApprovedBy = approver.id;
```

### 🔒 Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;

-- Budget access policies
CREATE POLICY "Users can view budgets for their projects" ON budgets
  FOR SELECT USING (
    ProjectId IN (
      SELECT id FROM projects 
      WHERE lead_id = auth.uid() 
      OR created_by = auth.uid()
      OR affiliate_user = auth.uid()::text
    )
  );

-- Client access policies  
CREATE POLICY "Users can view clients they created" ON clients
  FOR SELECT USING (created_by = auth.uid()::text);
```

## Summary

The current database schema is well-structured but needs several enhancements:

1. **✅ Strong Foundation**: Core tables and relationships exist
2. **⚠️ Missing Constraints**: Need enums, check constraints, and foreign keys
3. **⚠️ Missing Indexes**: Performance optimization needed
4. **⚠️ Missing Functions**: Validation and automation triggers needed
5. **⚠️ Missing Security**: RLS policies need implementation

**Next Steps**: Implement the missing elements identified above to ensure data integrity, performance, and security.
