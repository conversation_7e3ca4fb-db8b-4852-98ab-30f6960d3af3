'use client';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useStatus } from '@/hooks/use-db';
import { StatusIcon } from '@/lib/constants/status';
import { CheckIcon } from 'lucide-react';
import { useEffect, useId, useState } from 'react';

interface StatusOption {
  id: string;
  name: string;
  color: string;
  sort_order: number;
}

interface StatusWithPercentProps {
  status: StatusOption | null;
  percentComplete: number;
  onStatusChange?: (statusId: string) => void;
}

export function StatusWithPercent({
  status,
  percentComplete,
  onStatusChange,
}: StatusWithPercentProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(status?.id || '');
  const { status: statusOptions, loading } = useStatus();

  const handleStatusChange = (statusId: string) => {
    setValue(statusId);
    setOpen(false);

    if (onStatusChange) {
      onStatusChange(statusId);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          id={id}
          className='flex items-center justify-center gap-1.5'
          size='sm'
          variant='ghost'
          // role='combobox'
          aria-expanded={open}
        >
          {status ? (
            <StatusIcon statusId={status.id} />
          ) : (
            <div className='w-3 h-3 rounded-full bg-gray-300' />
          )}
          <span className='text-xs font-medium mt-[1px]'>
            {percentComplete}%
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='border-input w-48 p-0' align='start'>
        <Command>
          <CommandInput placeholder='Set status...' />
          <CommandList>
            <CommandEmpty>No status found.</CommandEmpty>
            <CommandGroup>
              {loading ? (
                <CommandItem disabled>
                  <span>Loading statuses...</span>
                </CommandItem>
              ) : (
                statusOptions.map((item) => (
                  <CommandItem
                    key={item.id}
                    value={item.id}
                    onSelect={handleStatusChange}
                    className='flex items-center justify-between'
                  >
                    <div className='flex items-center gap-2'>
                      <StatusIcon statusId={item.id} />
                      <span className='text-xs'>{item.name}</span>
                    </div>
                    {(value === item.id || status?.id === item.id) && (
                      <CheckIcon size={14} className='ml-auto' />
                    )}
                  </CommandItem>
                ))
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
