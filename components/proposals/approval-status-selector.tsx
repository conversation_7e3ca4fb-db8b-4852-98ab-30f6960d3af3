'use client';

import { Check, Clock, X } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Proposal } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';

interface ApprovalStatusSelectorProps {
  proposal: Proposal;
  onApprovalChange: (proposalId: number, approved: boolean | null) => void;
}

const ApprovalStatusIcon = ({ status }: { status: boolean | null }) => {
  if (status === true) {
    return <Check className='h-4 w-4 text-green-600' />;
  }
  if (status === false) {
    return <X className='h-4 w-4 text-red-600' />;
  }
  return <Clock className='h-4 w-4 text-yellow-600' />;
};

const getStatusText = (status: boolean | null): string => {
  if (status === true) return 'Approved';
  if (status === false) return 'Rejected';
  return 'Pending';
};

const getStatusColor = (status: boolean | null): string => {
  if (status === true) return 'text-green-600';
  if (status === false) return 'text-red-600';
  return 'text-yellow-600';
};

export function ApprovalStatusSelector({
  proposal,
  onApprovalChange,
}: ApprovalStatusSelectorProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  // Check if the proposal status can be changed (only pending proposals can be changed)
  const canChangeStatus = proposal.is_approved === null;

  const handleStatusChange = (newStatus: boolean | null) => {
    if (isUpdating || !canChangeStatus) return;

    setIsUpdating(true);
    onApprovalChange(proposal.id, newStatus);

    // Reset updating state after a short delay
    setTimeout(() => {
      setIsUpdating(false);
    }, 1000);
  };

  // If status cannot be changed, render as a static display
  if (!canChangeStatus) {
    return (
      <div
        className={cn(
          'h-8 px-2 flex items-center gap-2 rounded-md',
          getStatusColor(proposal.is_approved)
        )}
      >
        <ApprovalStatusIcon status={proposal.is_approved} />
        <span className='text-sm font-medium'>
          {getStatusText(proposal.is_approved)}
        </span>
      </div>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant='ghost'
          size='sm'
          className={cn(
            'h-8 px-2 flex items-center gap-2',
            getStatusColor(proposal.is_approved),
            isUpdating && 'opacity-50 cursor-not-allowed'
          )}
          disabled={isUpdating}
        >
          <ApprovalStatusIcon status={proposal.is_approved} />
          <span className='text-sm font-medium'>
            {getStatusText(proposal.is_approved)}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='start'>
        <DropdownMenuItem
          onClick={() => handleStatusChange(null)}
          className='flex items-center gap-2'
        >
          <Clock className='h-4 w-4 text-yellow-600' />
          <span>Pending</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange(true)}
          className='flex items-center gap-2'
        >
          <Check className='h-4 w-4 text-green-600' />
          <span>Approved</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleStatusChange(false)}
          className='flex items-center gap-2'
        >
          <X className='h-4 w-4 text-red-600' />
          <span>Rejected</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
