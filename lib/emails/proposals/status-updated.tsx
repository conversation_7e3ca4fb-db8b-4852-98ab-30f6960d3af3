import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import type { ProposalStatusUpdateEmailData } from '@/lib/types/email-types';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const ProposalStatusUpdate = (data: ProposalStatusUpdateEmailData) => {
  const { proposal, oldStatus, newStatus, affiliate, triggeredBy } = data;

  return (
    <Html>
      <Head />
      <Preview>Proposal Status Updated: {proposal.title}</Preview>
      <Body style={main}>
        {/* Header Section */}
        <Container
          style={{
            ...container,
            backgroundColor: colors['100'],
          }}
        >
          <Img
            src={`${baseUrl}/thehuefactory_hero.png`}
            width='100%'
            height='auto'
            alt='Email Header Image'
          />
        </Container>

        {/* Header Transition */}
        <Container
          style={{
            margin: '0 auto',
            backgroundColor: colors['100'],
            alignItems: 'center',
            alignContent: 'center',
            textAlign: 'center',
          }}
        >
          <Section
            style={{
              backgroundColor: 'white',
              height: 20,
              borderTopLeftRadius: '16px',
              borderTopRightRadius: '16px',
            }}
          ></Section>
        </Container>

        {/* Main Content */}
        <Container style={container}>
          <Heading style={h1}>Proposal Status Updated</Heading>

          <Text style={{ ...text, marginBottom: '24px' }}>
            We wanted to update you on the status of your proposal submission.
          </Text>

          {/* Proposal Details */}
          <Container style={proposalCard}>
            <Heading style={proposalTitle}>{proposal.title}</Heading>

            {proposal.description && (
              <Text style={{ ...text, marginBottom: '16px' }}>
                {proposal.description}
              </Text>
            )}

            {/* Status Change Highlight */}
            <Container style={statusChangeContainer}>
              <Container style={statusItem}>
                <Text style={statusLabel}>Previous Status:</Text>
                <Text style={statusValue}>{oldStatus}</Text>
              </Container>

              <Text style={statusArrow}>→</Text>

              <Container style={statusItem}>
                <Text style={statusLabel}>New Status:</Text>
                <Text
                  style={{
                    ...statusValue,
                    color: colors[100],
                    fontWeight: 'bold',
                  }}
                >
                  {newStatus}
                </Text>
              </Container>
            </Container>

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Client:</Text>
              <Text style={detailValue}>{proposal.client_name}</Text>
            </Container>

            {proposal.budget && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Budget:</Text>
                <Text style={detailValue}>
                  ${proposal.budget.toLocaleString()}
                </Text>
              </Container>
            )}

            {proposal.timeline && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Timeline:</Text>
                <Text style={detailValue}>{proposal.timeline}</Text>
              </Container>
            )}

            {affiliate && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Affiliate:</Text>
                <Text style={detailValue}>{affiliate.full_name}</Text>
              </Container>
            )}

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Updated by:</Text>
              <Text style={detailValue}>{triggeredBy.full_name}</Text>
            </Container>
          </Container>

          {/* Action Button */}
          <Container style={{ textAlign: 'center', marginTop: '32px' }}>
            <Link
              href={`https://team.thehuefactory.co/admin/proposals/${proposal.id}`}
              target='_blank'
              style={{
                backgroundColor: colors[100],
                paddingRight: 18,
                paddingLeft: 18,
                fontWeight: 'bold',
                color: 'white',
                paddingTop: 16,
                paddingBottom: 16,
                borderRadius: 32,
                whiteSpace: 'nowrap',
                fontFamily: 'monospace',
                textDecoration: 'none',
                display: 'inline-block',
              }}
            >
              View Proposal Details
            </Link>
          </Container>

          <Text style={{ ...text, marginBottom: '24px', marginTop: '32px' }}>
            We'll continue to keep you updated as your proposal progresses
            through our review process. Thank you for your patience and for
            choosing thehuefactory for your creative needs.
          </Text>

          <Text style={{ ...text, marginBottom: '24px' }}>
            If you have any questions about this status update, please feel free
            to{' '}
            <Link
              href='https://www.thehuefactory.co/contact'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              contact us
            </Link>{' '}
            or reply to this email.
          </Text>
        </Container>

        {/* Footer Section */}
        <Container
          style={{
            ...container,
            marginTop: '48px',
          }}
        >
          <Img
            src={`${baseUrl}/Logo_3dicon_orange.png`}
            width='42'
            height='42'
            alt="thehuefactory's Logo"
          />
          <Text style={{ ...footer, marginTop: '40px' }}>
            <Link
              href='https://www.thehuefactory.co/'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              thehuefactory.co
            </Link>{' '}
            <br />
            The Creative Powerhouse.
            <br />
            Copyright © 2024 thehuefactory. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default ProposalStatusUpdate;

// Styles following existing pattern
const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const proposalCard = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '24px',
  marginBottom: '24px',
};

const proposalTitle = {
  color: colors[100],
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const detailsContainer = {
  display: 'flex',
  marginBottom: '8px',
  alignItems: 'center',
};

const detailLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
  minWidth: '120px',
};

const detailValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0',
};

const statusChangeContainer = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '24px',
  padding: '16px',
  backgroundColor: '#fff',
  border: '2px solid #e9ecef',
  borderRadius: '8px',
};

const statusItem = {
  textAlign: 'center' as const,
  flex: '1',
};

const statusLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  fontWeight: 'bold',
  margin: '0 0 8px 0',
  textTransform: 'uppercase' as const,
};

const statusValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '16px',
  margin: '0',
};

const statusArrow = {
  color: colors[100],
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 16px',
};
