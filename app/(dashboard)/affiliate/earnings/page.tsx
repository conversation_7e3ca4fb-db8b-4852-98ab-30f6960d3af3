'use client';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { CommissionDashboard } from '@/components/affiliate/commission-dashboard';
import { CommissionTracker } from '@/components/affiliate/commission-tracker';

export default function AffiliateEarningsPage() {
  return (
    <main className="h-full flex flex-col">
      <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
        <p className="font-medium text-sm">Earnings & Commissions</p>
      </header>
      <section className="flex-1 p-6">
        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="tracker">Commission Tracker</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard" className="space-y-6 mt-6">
            <CommissionDashboard />
          </TabsContent>
          
          <TabsContent value="tracker" className="space-y-6 mt-6">
            <CommissionTracker />
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
}
