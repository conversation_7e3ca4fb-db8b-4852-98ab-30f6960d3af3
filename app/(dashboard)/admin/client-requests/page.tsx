'use client';
import { ProposalsMetrics } from '@/components/proposals/proposals-metrics';
import { ProposalsTable } from '@/components/tables/proposals/proposals-table';
import { useProposals } from '@/hooks/use-db';

export default function Page() {
  const { proposals, loading } = useProposals();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Client Requests</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <ProposalsMetrics proposals={proposals} loading={loading} />
        <ProposalsTable />
      </section>
    </main>
  );
}
