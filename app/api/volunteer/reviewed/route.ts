import { ReviewedApplication } from '@/lib/emails/volunteers';
import { Database } from '@/lib/supabase/database-types';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

type dataProps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export async function POST(request: Request) {
  const form: dataProps = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email!],
      bcc: ['<EMAIL>'],
      subject: 'Your Application Has Been Reviewed.',
      text: '',
      react: ReviewedApplication(form),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
