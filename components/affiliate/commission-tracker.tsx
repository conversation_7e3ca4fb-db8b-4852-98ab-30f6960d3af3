'use client';

import { 
  DollarSign, 
  Calendar, 
  Building, 
  User,
  CheckCircle,
  Clock,
  AlertTriangle,
  Eye,
  Download
} from 'lucide-react';
import { useState, useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useBudgets, useProjects, useClients, useProfile } from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/format-currency';
import { cn } from '@/lib/utils/cn';

interface CommissionTrackerProps {
  className?: string;
}

export function CommissionTracker({ className }: CommissionTrackerProps) {
  const { budgets } = useBudgets();
  const { projects } = useProjects();
  const { clients } = useClients();
  const { profile } = useProfile();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCommission, setSelectedCommission] = useState<Budget | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);

  // Get affiliate commissions
  const affiliateCommissions = useMemo(() => {
    if (!profile?.id) return [];

    return budgets
      .filter(budget => budget.has_affiliate && budget.affiliateId === profile.id)
      .map(budget => {
        const project = projects.find(p => p.id === budget.ProjectId);
        const client = clients.find(c => c.id === budget.ClientId);
        
        return {
          ...budget,
          project,
          client,
        };
      });
  }, [budgets, projects, clients, profile?.id]);

  // Filter commissions
  const filteredCommissions = useMemo(() => {
    let filtered = affiliateCommissions;

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(commission => 
        commission.project?.name.toLowerCase().includes(term) ||
        commission.client?.name.toLowerCase().includes(term) ||
        commission.Category.toLowerCase().includes(term)
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(commission => commission.Status === statusFilter);
    }

    return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [affiliateCommissions, searchTerm, statusFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'locked':
        return 'bg-yellow-100 text-yellow-800';
      case 'spent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'locked':
        return <AlertTriangle className="h-4 w-4" />;
      case 'spent':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const calculateCommissionStatus = (budget: Budget) => {
    switch (budget.Status) {
      case 'draft':
        return 'Pending Approval';
      case 'approved':
        return 'Active Project';
      case 'locked':
        return 'Project Locked';
      case 'spent':
        return 'Commission Earned';
      default:
        return 'Unknown';
    }
  };

  const exportCommissions = () => {
    // Create CSV data
    const csvData = filteredCommissions.map(commission => ({
      'Project': commission.project?.name || 'Unknown',
      'Client': commission.client?.name || 'Unknown',
      'Budget Amount': commission.ActualAmount,
      'Commission Amount': commission.AffiliateCommission || 0,
      'Commission Rate': commission.AffiliateCommission ? ((commission.AffiliateCommission / commission.ActualAmount) * 100).toFixed(2) + '%' : '0%',
      'Status': commission.Status,
      'Category': commission.Category,
      'Start Date': formatDate(commission.StartDate),
      'End Date': formatDate(commission.EndDate),
      'Created': formatDate(commission.created_at),
    }));

    // Convert to CSV string
    const headers = Object.keys(csvData[0] || {});
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => headers.map(header => `"${row[header as keyof typeof row]}"`).join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `affiliate-commissions-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Commission Tracker
            </CardTitle>
            <CardDescription>
              Track your affiliate commissions and earnings
            </CardDescription>
          </div>
          <Button 
            onClick={exportCommissions}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
            disabled={filteredCommissions.length === 0}
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="Search commissions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="locked">Locked</SelectItem>
                <SelectItem value="spent">Spent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Commission Table */}
        {filteredCommissions.length > 0 ? (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Project</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Budget</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Rate</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCommissions.map((commission) => {
                  const commissionRate = commission.AffiliateCommission 
                    ? ((commission.AffiliateCommission / commission.ActualAmount) * 100)
                    : 0;
                  
                  return (
                    <TableRow key={commission.id}>
                      <TableCell>
                        <div className="font-medium">
                          {commission.project?.name || 'Unknown Project'}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {commission.Category}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          {commission.client?.name || 'Unknown Client'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(commission.ActualAmount, commission.Currency)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Remaining: {formatCurrency(commission.CurrentAmount, commission.Currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium text-green-600">
                          {formatCurrency(commission.AffiliateCommission || 0, commission.Currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {commissionRate.toFixed(1)}%
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(commission.Status)}
                          <Badge className={getStatusColor(commission.Status)}>
                            {commission.Status}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {calculateCommissionStatus(commission)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatDate(commission.StartDate)}</div>
                          <div className="text-muted-foreground">
                            to {formatDate(commission.EndDate)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={() => {
                            setSelectedCommission(commission);
                            setIsDetailDialogOpen(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No commissions found</p>
            <p className="text-sm">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your filters'
                : 'Submit proposals to start earning commissions'
              }
            </p>
          </div>
        )}
      </CardContent>

      {/* Commission Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Commission Details</DialogTitle>
            <DialogDescription>
              Detailed information about this commission
            </DialogDescription>
          </DialogHeader>
          {selectedCommission && (
            <div className="space-y-6">
              {/* Commission Overview */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm font-medium">Commission Amount</div>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(selectedCommission.AffiliateCommission || 0, selectedCommission.Currency)}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm font-medium">Commission Rate</div>
                  <div className="text-2xl font-bold">
                    {selectedCommission.AffiliateCommission 
                      ? ((selectedCommission.AffiliateCommission / selectedCommission.ActualAmount) * 100).toFixed(1)
                      : 0
                    }%
                  </div>
                </div>
              </div>

              {/* Project Information */}
              <div className="space-y-4">
                <h3 className="font-medium">Project Information</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Project Name</div>
                    <div className="text-muted-foreground">
                      {selectedCommission.project?.name || 'Unknown Project'}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Client</div>
                    <div className="text-muted-foreground">
                      {selectedCommission.client?.name || 'Unknown Client'}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Category</div>
                    <div className="text-muted-foreground">
                      {selectedCommission.Category}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Status</div>
                    <Badge className={getStatusColor(selectedCommission.Status)}>
                      {selectedCommission.Status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Budget Information */}
              <div className="space-y-4">
                <h3 className="font-medium">Budget Information</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Total Budget</div>
                    <div className="text-muted-foreground">
                      {formatCurrency(selectedCommission.ActualAmount, selectedCommission.Currency)}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Remaining</div>
                    <div className="text-muted-foreground">
                      {formatCurrency(selectedCommission.CurrentAmount, selectedCommission.Currency)}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Start Date</div>
                    <div className="text-muted-foreground">
                      {formatDate(selectedCommission.StartDate)}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">End Date</div>
                    <div className="text-muted-foreground">
                      {formatDate(selectedCommission.EndDate)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {selectedCommission.Notes && (
                <div className="space-y-2">
                  <div className="font-medium">Notes</div>
                  <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                    {selectedCommission.Notes}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
