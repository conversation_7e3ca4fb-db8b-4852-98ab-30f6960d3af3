'use client';

import { useMemo } from 'react';
import {
  TrendingDown,
  TrendingUp,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { Proposal } from '@/lib/supabase/database-modules';

interface ProposalsMetricsProps {
  proposals: Proposal[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  trend?: 'up' | 'down' | 'neutral';
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
}

function MetricCard({
  title,
  value,
  subtitle,
  trend,
  variant = 'default',
  icon,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  const getTrendIcon = () => {
    if (trend === 'up')
      return <TrendingUp className='h-4 w-4 text-green-600' />;
    if (trend === 'down')
      return <TrendingDown className='h-4 w-4 text-red-600' />;
    return null;
  };

  return (
    <Card className={`${getVariantStyles()} transition-all`}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium text-muted-foreground'>
          {title}
        </CardTitle>
        {icon && <div className='text-muted-foreground'>{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className='flex items-center justify-between'>
          <div>
            <div className='text-2xl font-bold'>{value}</div>
            <div className='flex items-center gap-1 text-xs text-muted-foreground'>
              {getTrendIcon()}
              <span>{subtitle}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function ProposalsMetrics({
  proposals,
  loading,
}: ProposalsMetricsProps) {
  const metrics = useMemo(() => {
    if (!proposals.length) {
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        completed: 0,
        recentProposals: 0,
      };
    }

    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const total = proposals.length;
    const pending = proposals.filter(
      (proposal) => proposal.is_approved === null
    ).length;
    const approved = proposals.filter(
      (proposal) => proposal.is_approved === true
    ).length;
    const rejected = proposals.filter(
      (proposal) => proposal.is_approved === false
    ).length;
    const completed = proposals.filter(
      (proposal) => proposal.completed === true
    ).length;
    const recentProposals = proposals.filter(
      (proposal) => new Date(proposal.created_at) >= lastWeek
    ).length;

    return {
      total,
      pending,
      approved,
      rejected,
      completed,
      recentProposals,
    };
  }, [proposals]);

  if (loading) {
    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {['total', 'pending', 'approved', 'completed'].map((metric) => (
          <Card key={metric} className='animate-pulse'>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <div className='h-4 w-20 bg-muted'></div>
              <div className='h-4 w-4 bg-muted'></div>
            </CardHeader>
            <CardContent>
              <div className='h-8 w-16 bg-muted  mb-2'></div>
              <div className='h-3 w-24 bg-muted '></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const approvalRate =
    metrics.total > 0
      ? Math.round((metrics.approved / metrics.total) * 100)
      : 0;
  const pendingPercentage =
    metrics.total > 0 ? Math.round((metrics.pending / metrics.total) * 100) : 0;
  const completionRate =
    metrics.approved > 0
      ? Math.round((metrics.completed / metrics.approved) * 100)
      : 0;

  return (
    <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
      <MetricCard
        title='Total Proposals'
        value={metrics.total}
        subtitle={`${metrics.recentProposals} new this week`}
        trend={metrics.recentProposals > 0 ? 'up' : 'neutral'}
        icon={<FileText className='h-4 w-4' />}
      />

      <MetricCard
        title='Pending Review'
        value={metrics.pending}
        subtitle={`${pendingPercentage}% awaiting decision`}
        variant={metrics.pending > 5 ? 'warning' : 'default'}
        icon={<Clock className='h-4 w-4' />}
      />

      <MetricCard
        title='Approved'
        value={metrics.approved}
        subtitle={`${approvalRate}% approval rate`}
        variant='success'
        trend={
          approvalRate > 60 ? 'up' : approvalRate < 30 ? 'down' : 'neutral'
        }
        icon={<CheckCircle className='h-4 w-4' />}
      />

      <MetricCard
        title='Completed'
        value={metrics.completed}
        subtitle={`${completionRate}% of approved`}
        variant={metrics.completed > 0 ? 'success' : 'default'}
        icon={<XCircle className='h-4 w-4' />}
      />
    </div>
  );
}
