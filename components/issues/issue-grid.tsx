'use client';

import { format } from 'date-fns';
import { motion } from 'motion/react';
import { useEffect, useRef, memo } from 'react';
import {
  type DragSourceMonitor,
  useDrag,
  useDragLayer,
  useDrop,
} from 'react-dnd';
import { getEmptyImage } from 'react-dnd-html5-backend';

import { ContextMenu, ContextMenuTrigger } from '@/components/ui/context-menu';
import type { Issue } from '@/lib/supabase/database-modules';

import { AssigneeUser } from './assignee-user';
import { IssueContextMenu } from './issue-context-menu';
import { LabelBadge } from './label-badge';
import { PrioritySelector } from './priority-selector';
import { ProjectBadge } from './project-badge';
import { StatusSelector } from './status-selector';

export const IssueDragType = 'ISSUE';
type IssueGridProps = {
  issue: Issue;
};

interface PriorityData {
  id: string;
  name: string;
  icon_name?: string | null;
  sort_order?: number | null;
}

interface StatusData {
  id: string;
  name: string;
  color: string;
  sort_order?: number | null;
}

interface UserData {
  id: string;
  full_name?: string | null;
  name?: string;
  email: string;
  avatar_url?: string | null;
}

interface LabelData {
  id: string;
  name: string;
  color: string;
}

// Helper functions to convert database types to component types
const convertPriority = (priority: PriorityData | null | undefined) => {
  if (!priority) return null;
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name || '',
    sort_order: priority.sort_order || 0,
  };
};

const convertStatus = (status: StatusData | null | undefined) => {
  if (!status) return null;
  return {
    id: status.id,
    name: status.name,
    color: status.color,
    sort_order: status.sort_order || 0,
  };
};

const convertUser = (user: UserData | null | undefined) => {
  if (!user) return null;
  return {
    id: user.id,
    name: user.full_name || user.name || user.email,
    email: user.email,
    avatar_url: user.avatar_url,
  };
};

const convertLabels = (labels: LabelData[] | null | undefined) => {
  if (!labels) return [];
  return labels.map((label) => ({
    id: label.id,
    name: label.name,
    color: label.color,
  }));
};

// Custom DragLayer component to render the drag preview
function IssueDragPreview({ issue }: { issue: Issue }) {
  return (
    <div className='w-full p-3 bg-background rounded-md border border-border/50 overflow-hidden'>
      <div className='flex items-center justify-between mb-2'>
        <div className='flex items-center gap-1.5'>
          <PrioritySelector
            priority={convertPriority(issue.priority)}
            issueId={issue.id}
          />
          <span className='text-xs text-muted-foreground font-medium'>
            {issue.identifier}
          </span>
        </div>
        <StatusSelector
          status={convertStatus(issue.status)}
          issueId={issue.id}
        />
      </div>

      <h3 className='text-sm font-semibold mb-3 line-clamp-2'>{issue.title}</h3>

      <div className='flex flex-wrap gap-1.5 mb-3 min-h-[1.5rem]'>
        <LabelBadge labels={convertLabels(issue.labels || [])} />
        {issue.project && <ProjectBadge project={issue.project} />}
      </div>

      <div className='flex items-center justify-between mt-auto pt-2'>
        <span className='text-xs text-muted-foreground'>
          {(() => {
            const date = new Date(issue.created_at);
            return Number.isNaN(date.getTime())
              ? 'Invalid'
              : format(date, 'MMM dd');
          })()}
        </span>
        <AssigneeUser user={convertUser(issue.assignee)} />
      </div>
    </div>
  );
}

// Custom DragLayer to show custom preview during drag
export function CustomDragLayer() {
  const { itemType, isDragging, item, currentOffset } = useDragLayer(
    (monitor) => ({
      item: monitor.getItem() as Issue,
      itemType: monitor.getItemType(),
      currentOffset: monitor.getSourceClientOffset(),
      isDragging: monitor.isDragging(),
    })
  );

  if (!isDragging || itemType !== IssueDragType || !currentOffset) {
    return null;
  }

  return (
    <div
      className='fixed pointer-events-none z-50 left-0 top-0'
      style={{
        transform: `translate(${currentOffset.x}px, ${currentOffset.y}px)`,
        width: '348px', // Match the width of your cards
      }}
    >
      <IssueDragPreview issue={item} />
    </div>
  );
}

export const IssueGrid = memo(function IssueGrid({ issue }: IssueGridProps) {
  const ref = useRef<HTMLDivElement>(null);

  // Set up drag functionality.
  const [{ isDragging }, drag, preview] = useDrag(() => ({
    type: IssueDragType,
    item: issue,
    collect: (monitor: DragSourceMonitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  // Use empty image as drag preview (we'll create a custom one with DragLayer)
  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  // Set up drop functionality.
  const [, drop] = useDrop(() => ({
    accept: IssueDragType,
  }));

  // Connect drag and drop to the element.
  drag(drop(ref));

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <motion.div
          ref={ref}
          className='w-full p-3 bg-background rounded-md shadow-xs border border-border/50 cursor-default'
          layoutId={`issue-grid-${issue.identifier}`}
          style={{
            opacity: isDragging ? 0.5 : 1,
            cursor: isDragging ? 'grabbing' : 'default',
          }}
        >
          <div className='flex items-center justify-between mb-2'>
            <div className='flex items-center gap-1.5'>
              <PrioritySelector
                priority={convertPriority(issue.priority)}
                issueId={issue.id}
              />
              <span className='text-xs text-muted-foreground font-medium'>
                {issue.identifier}
              </span>
            </div>
            <StatusSelector
              status={convertStatus(issue.status)}
              issueId={issue.id}
            />
          </div>
          <h3 className='text-sm font-semibold mb-3 line-clamp-2'>
            {issue.title}
          </h3>
          <div className='flex flex-wrap gap-1.5 mb-3 min-h-[1.5rem]'>
            <LabelBadge labels={convertLabels(issue.labels || [])} />
            {issue.project && <ProjectBadge project={issue.project} />}
          </div>
          <div className='flex items-center justify-between mt-auto pt-2'>
            <span className='text-xs text-muted-foreground'>
              {(() => {
                const date = new Date(issue.created_at);
                return Number.isNaN(date.getTime())
                  ? 'Invalid'
                  : format(date, 'MMM dd');
              })()}
            </span>
            <AssigneeUser user={convertUser(issue.assignee)} />
          </div>
        </motion.div>
      </ContextMenuTrigger>
      <IssueContextMenu issueId={issue.id} />
    </ContextMenu>
  );
});
