import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { IssueStatusUpdate } from '@/lib/emails/issues/status-updated';
import { email<PERSON>rror<PERSON>and<PERSON> } from '@/lib/services/email-error-handler';
import type { IssueStatusUpdateEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request) {
  return new Promise((resolve, reject) => {
    let emailData: IssueStatusUpdateEmailData;
    let emailId: string;

    request
      .json()
      .then((data: IssueStatusUpdateEmailData) => {
        emailData = data;
        emailId = emailErrorHandler.generateEmailId(
          'issue_status_update',
          emailData.issue.id
        );

        // Validate required data
        if (
          !emailData.issue ||
          !emailData.project ||
          !emailData.oldStatus ||
          !emailData.newStatus ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: issue, project, oldStatus, newStatus, or recipients'
          );
        }

        // Create email sending function for retry logic
        const sendEmailFunction = () => {
          // Create email template with data
          const emailTemplate = IssueStatusUpdate(emailData);

          // Render email with enhanced formatting
          return render(emailTemplate)
            .then((rendered) => {
              return Promise.all([
                pretty(rendered),
                render(IssueStatusUpdate(emailData), { plainText: true }),
              ]);
            })
            .then(([html, text]) => {
              return resend.emails.send({
                from: 'Thehuefactory <<EMAIL>>',
                replyTo: '<EMAIL>',
                to: emailData.recipients,
                bcc: ['<EMAIL>'],
                subject: `Issue Status Updated: ${emailData.issue.title}`,
                react: IssueStatusUpdate(emailData),
                html: html,
                text: text,
              });
            });
        };

        // Use error handler with retry logic
        return emailErrorHandler.sendEmailWithRetry(
          emailId,
          emailData as unknown as Record<string, unknown>,
          sendEmailFunction
        );
      })
      .then((emailResult) => {
        const result = emailResult as { data?: { id?: string } };
        console.log('Issue status update email sent successfully:', {
          id: result.data?.id,
          subject: `Issue Status Updated: ${emailData.issue.title}`,
          recipients: emailData.recipients,
          emailId: emailId,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: result.data?.id,
            emailId: emailId,
          })
        );
      })
      .catch((error) => {
        console.error('Issue status update email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send issue status update email',
            emailId: emailId,
          })
        );
      });
  });
}
