'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { useMembers } from '@/hooks/use-db';
import type { Member } from '@/lib/supabase/database-modules';

import { createColumns } from './columns';

interface MembersTableProps {
  filteredMembers?: Member[];
}

export function MembersTable({ filteredMembers }: MembersTableProps = {}) {
  const { members, loading, error, updateMember, deleteMember } = useMembers();
  const router = useRouter();

  // Use filtered members if provided, otherwise use all members
  const displayMembers = filteredMembers || members;

  const handleViewMember = (member: Member) => {
    router.push(`/admin/members/${member.id}`);
  };

  const handleDeleteMember = (member: Member) => {
    const memberName = member.full_name || member.name;
    if (confirm(`Are you sure you want to remove ${memberName}?`)) {
      const deletePromise = deleteMember(member.id);

      toast.promise(deletePromise, {
        loading: `Removing ${memberName}...`,
        success: `${memberName} has been removed successfully`,
        error: (error) =>
          `Failed to remove member: ${error?.message || 'Unknown error'}`,
      });
    }
  };

  const handleRoleChange = async (
    memberId: string,
    role: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
  ): Promise<void> => {
    await updateMember(memberId, { role });
  };

  const columns = createColumns({
    onViewMember: handleViewMember,
    onDeleteMember: handleDeleteMember,
    onRoleChange: handleRoleChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <>
      <DataTable columns={columns} data={displayMembers} isLoading={loading} />

      {/* TODO: Add view/edit member dialogs */}
    </>
  );
}
