'use client';

import { Plus } from 'lucide-react';
import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { TeamsMetrics } from '@/components/teams/teams-metrics';
import { TeamsTable } from '@/components/tables/teams/teams-table';
import { useTeams, useTeamMembers, useIssues, useCycles } from '@/hooks/use-db';

export default function TeamsPage() {
  const { teams, loading } = useTeams();
  const { teamMembers, loading: membersLoading } = useTeamMembers();
  const { issues } = useIssues();
  const { cycles } = useCycles();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Teams</p>
        <Link href='/admin/teams/new'>
          <Button size='sm' className='h-8'>
            <Plus className='h-4 w-4 mr-2' />
            New Team
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <TeamsMetrics
          teams={teams}
          teamMembers={teamMembers}
          issues={issues}
          cycles={cycles}
          loading={loading || membersLoading}
        />
        <TeamsTable />
      </section>
    </main>
  );
}
