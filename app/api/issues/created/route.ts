import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { IssueCreated } from '@/lib/emails/issues/created';
import type { IssueCreatedEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request) {
  return new Promise((resolve, reject) => {
    let emailData: IssueCreatedEmailData;

    request
      .json()
      .then((data: IssueCreatedEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.issue ||
          !emailData.project ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: issue, project, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = IssueCreated(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(IssueCreated(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `New Issue Created: ${emailData.issue.title}`,
          react: IssueCreated(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Issue created email sent successfully:', {
          id: emailResult.data?.id,
          subject: `New Issue Created: ${emailData.issue.title}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Issue created email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send issue created email',
          })
        );
      });
  });
}
