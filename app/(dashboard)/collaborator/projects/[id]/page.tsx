'use client';

import {
  AlertCircle,
  Save,
  Users,
  X,
  Calendar,
  Target,
  Plus,
  FolderOpen,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { DatePicker } from '@/components/projects/date-picker';
import { HealthPopover } from '@/components/projects/health-popover';
import { PrioritySelector } from '@/components/projects/priority-selector';
import { StatusWithPercent } from '@/components/projects/status-with-percent';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataTable } from '@/components/ui/data-table';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { createColumns } from '@/components/tables/issues/columns';
import {
  useProjects,
  useProfile,
  useProjectMembers,
  useIssues,
} from '@/hooks/use-db';
import type {
  Project,
  ProjectMember,
  Issue,
} from '@/lib/supabase/database-modules';

export default function CollaboratorProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const { fetchProject, updateProject, checkProjectAccess } = useProjects();
  const { profile } = useProfile();
  const { fetchProjectMembersByProject } = useProjectMembers();
  const { getProjectIssues } = useIssues();

  const [project, setProject] = useState<Project | null>(null);
  const [projectMembers, setProjectMembers] = useState<ProjectMember[]>([]);
  const [projectIssues, setProjectIssues] = useState<Issue[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [editedName, setEditedName] = useState('');
  const [editedDescription, setEditedDescription] = useState('');

  // Check access and load project data
  useEffect(() => {
    if (!profile?.id || !projectId) return;

    setLoading(true);

    // First check if user has access to this project
    checkProjectAccess(projectId, profile.id)
      .then((access) => {
        if (!access) {
          toast.error('You do not have access to this project');
          router.push('/collaborator/projects');
          return;
        }

        setHasAccess(true);

        // Load project details
        return fetchProject(projectId);
      })
      .then((projectData) => {
        if (projectData) {
          setProject(projectData);
          setEditedName(projectData.name);
          setEditedDescription(projectData.description || '');
        }
      })
      .catch((error) => {
        console.error('Error loading project:', error);
        toast.error('Failed to load project');
        router.push('/collaborator/projects');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [projectId, profile?.id, checkProjectAccess, fetchProject, router]);

  // Load project members
  useEffect(() => {
    if (!hasAccess || !projectId) return;

    fetchProjectMembersByProject(projectId)
      .then((members) => {
        setProjectMembers(members);
      })
      .catch((error) => {
        console.error('Error loading project members:', error);
      });
  }, [projectId, hasAccess, fetchProjectMembersByProject]);

  // Load project issues
  useEffect(() => {
    if (!hasAccess || !projectId) return;

    getProjectIssues(projectId)
      .then((issues) => {
        setProjectIssues(issues);
      })
      .catch((error) => {
        console.error('Error loading project issues:', error);
      });
  }, [projectId, hasAccess, getProjectIssues]);

  // Update handlers
  const handleUpdateProject = async (updates: Partial<Project>) => {
    if (!project) return;

    try {
      await updateProject(project.id, updates);
      toast.success('Project updated successfully');
      // Refresh project data
      const updatedProject = await fetchProject(project.id);
      if (updatedProject) {
        setProject(updatedProject);
      }
    } catch (error) {
      toast.error('Failed to update project');
      console.error('Error updating project:', error);
    }
  };

  const handleSaveName = () => {
    if (editedName.trim() && editedName !== project?.name) {
      handleUpdateProject({ name: editedName.trim() });
    }
    setIsEditingName(false);
  };

  const handleSaveDescription = () => {
    if (editedDescription !== project?.description) {
      handleUpdateProject({ description: editedDescription || null });
    }
    setIsEditingDescription(false);
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>
          <p className='text-muted-foreground'>Loading project...</p>
        </div>
      </div>
    );
  }

  if (!hasAccess || !project) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='text-center'>
          <AlertCircle className='h-12 w-12 text-red-500 mx-auto mb-4' />
          <h2 className='text-xl font-semibold mb-2'>Access Denied</h2>
          <p className='text-muted-foreground mb-4'>
            You do not have permission to view this project.
          </p>
          <Button onClick={() => router.push('/collaborator/projects')}>
            Back to Projects
          </Button>
        </div>
      </div>
    );
  }

  const issueMetrics = {
    total: projectIssues.length,
    open: projectIssues.filter(
      (issue) =>
        issue.status?.name !== 'Done' && issue.status?.name !== 'Completed'
    ).length,
    inProgress: projectIssues.filter(
      (issue) =>
        issue.status?.name === 'In Progress' ||
        issue.status?.name === 'In Review'
    ).length,
    completed: projectIssues.filter(
      (issue) =>
        issue.status?.name === 'Done' || issue.status?.name === 'Completed'
    ).length,
    overdue: projectIssues.filter((issue) => {
      if (!issue.due_date || issue.status?.name === 'Done') return false;
      return new Date(issue.due_date) < new Date();
    }).length,
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-2'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.push('/collaborator/projects')}
          >
            ← Back to Projects
          </Button>
        </div>
        <div className='flex items-center gap-2'>
          <Button size='sm'>
            <Plus className='h-4 w-4 mr-2' />
            New Issue
          </Button>
        </div>
      </header>

      <div className='flex-1 p-6 space-y-6'>
        {/* Project Header */}
        <div className='space-y-4'>
          {/* Editable Project Name */}
          <div className='flex items-center gap-2'>
            {isEditingName ? (
              <div className='flex items-center gap-2 flex-1'>
                <Input
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  className='text-2xl font-semibold border-none p-0 h-auto'
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') handleSaveName();
                    if (e.key === 'Escape') {
                      setIsEditingName(false);
                      setEditedName(project.name);
                    }
                  }}
                  autoFocus
                />
                <Button size='sm' onClick={handleSaveName}>
                  <Save className='size-3' />
                </Button>
                <Button
                  size='sm'
                  variant='ghost'
                  onClick={() => {
                    setIsEditingName(false);
                    setEditedName(project.name);
                  }}
                >
                  <X className='size-3' />
                </Button>
              </div>
            ) : (
              <h1
                className='text-2xl font-semibold cursor-pointer hover:bg-muted/50 px-2 py-1 -mx-2 -my-1 transition-colors'
                onClick={() => setIsEditingName(true)}
              >
                {project.name}
              </h1>
            )}
          </div>

          {/* Editable Project Description */}
          <div>
            {isEditingDescription ? (
              <div className='space-y-2'>
                <Textarea
                  value={editedDescription}
                  onChange={(e) => setEditedDescription(e.target.value)}
                  placeholder='Add a project description...'
                  className='min-h-[80px]'
                  autoFocus
                />
                <div className='flex gap-2'>
                  <Button size='sm' onClick={handleSaveDescription}>
                    Save
                  </Button>
                  <Button
                    size='sm'
                    variant='ghost'
                    onClick={() => {
                      setIsEditingDescription(false);
                      setEditedDescription(project.description || '');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <p
                className='text-muted-foreground cursor-pointer hover:bg-muted/50 px-2 py-1 -mx-2 -my-1 transition-colors min-h-[24px]'
                onClick={() => setIsEditingDescription(true)}
              >
                {project.description || 'Click to add a description...'}
              </p>
            )}
          </div>
        </div>

        {/* Project Overview Cards */}
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Progress</CardTitle>
              <Target className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {project.percent_complete}%
              </div>
              <p className='text-xs text-muted-foreground'>
                {project.percent_complete === 100 ? 'Completed' : 'In Progress'}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Issues</CardTitle>
              <FolderOpen className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{issueMetrics.total}</div>
              <p className='text-xs text-muted-foreground'>
                {issueMetrics.open} open, {issueMetrics.completed} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Team Members
              </CardTitle>
              <Users className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{projectMembers.length}</div>
              <p className='text-xs text-muted-foreground'>
                Active collaborators
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Due Date</CardTitle>
              <Calendar className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {project.target_date
                  ? new Date(project.target_date).toLocaleDateString()
                  : 'Not set'}
              </div>
              <p className='text-xs text-muted-foreground'>Target completion</p>
            </CardContent>
          </Card>
        </div>

        {/* Project Details Grid */}
        <div className='grid gap-6 md:grid-cols-2'>
          {/* Project Status & Health */}
          <Card>
            <CardHeader>
              <CardTitle>Project Status</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Status & Progress
                </label>
                <StatusWithPercent
                  status={
                    project.status
                      ? {
                          id: project.status.id,
                          name: project.status.name,
                          color: project.status.color,
                          sort_order: 0, // Default sort order since it's not provided
                        }
                      : null
                  }
                  percentComplete={project.percent_complete}
                  onStatusChange={(statusId) => {
                    handleUpdateProject({
                      status_id: statusId,
                    });
                  }}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Priority
                </label>
                <PrioritySelector
                  priority={
                    project.priority
                      ? {
                          id: project.priority.id,
                          name: project.priority.name,
                          icon_name: project.priority.icon_name || '',
                          sort_order: project.priority.sort_order || 0,
                        }
                      : null
                  }
                  onPriorityChange={(priorityId) => {
                    handleUpdateProject({ priority_id: priorityId });
                  }}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Health Status
                </label>
                <HealthPopover
                  project={{
                    id: project.id,
                    name: project.name,
                    description: project.description,
                    health: project.health || {
                      id: project.health_id || 'no-update',
                      name:
                        project.health_id === 'on-track'
                          ? 'On Track'
                          : project.health_id === 'off-track'
                            ? 'Off Track'
                            : project.health_id === 'at-risk'
                              ? 'At Risk'
                              : 'No Update',
                      description:
                        project.health_id === 'on-track'
                          ? 'Project is progressing as planned'
                          : project.health_id === 'off-track'
                            ? 'Project is behind schedule or facing issues'
                            : project.health_id === 'at-risk'
                              ? 'Project may face delays or issues'
                              : 'No recent status update available',
                    },
                    lead: project.lead
                      ? {
                          id: project.lead.id,
                          name: project.lead.full_name || project.lead.email,
                          avatar_url: project.lead.avatar_url,
                        }
                      : undefined,
                  }}
                  onHealthChange={(healthId) => {
                    handleUpdateProject({
                      health_id: healthId as
                        | 'no-update'
                        | 'off-track'
                        | 'on-track'
                        | 'at-risk',
                    });
                  }}
                />
              </div>
            </CardContent>
          </Card>

          {/* Project Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Start Date
                </label>
                <DatePicker
                  date={
                    project.start_date
                      ? new Date(project.start_date)
                      : undefined
                  }
                  onDateChange={(date) => {
                    handleUpdateProject({
                      start_date: date?.toISOString() || null,
                    });
                  }}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Target Date
                </label>
                <DatePicker
                  date={
                    project.target_date
                      ? new Date(project.target_date)
                      : undefined
                  }
                  onDateChange={(date) => {
                    handleUpdateProject({
                      target_date: date?.toISOString() || null,
                    });
                  }}
                />
              </div>

              <div>
                <label className='text-sm font-medium mb-2 block'>
                  Project Lead
                </label>
                <div className='flex items-center gap-2 p-2 border border-border bg-muted/50'>
                  {project.lead ? (
                    <>
                      <div className='size-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium'>
                        {project.lead.full_name?.charAt(0) ||
                          project.lead.email?.charAt(0) ||
                          '?'}
                      </div>
                      <span className='text-sm'>
                        {project.lead.full_name || project.lead.email}
                      </span>
                    </>
                  ) : (
                    <span className='text-muted-foreground text-sm'>
                      No lead assigned
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Team Members */}
        {projectMembers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='grid gap-2 md:grid-cols-2 lg:grid-cols-3'>
                {projectMembers.map((member) => (
                  <div
                    key={member.id}
                    className='flex items-center gap-2 p-2 border border-border bg-muted/50'
                  >
                    <div className='size-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium'>
                      {member.profiles?.full_name?.charAt(0) ||
                        member.profiles?.email?.charAt(0) ||
                        '?'}
                    </div>
                    <div className='flex-1 min-w-0'>
                      <p className='text-sm font-medium truncate'>
                        {member.profiles?.full_name || member.profiles?.email}
                      </p>
                      <p className='text-xs text-muted-foreground capitalize'>
                        {member.role}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Issues Section */}
        <div className='space-y-6'>
          {/* Issue Metrics */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium text-muted-foreground'>
                  Total Issues
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{issueMetrics.total}</div>
                <p className='text-xs text-muted-foreground'>
                  All issues in project
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium text-muted-foreground'>
                  Open Issues
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{issueMetrics.open}</div>
                <p className='text-xs text-muted-foreground'>
                  Awaiting work or review
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium text-muted-foreground'>
                  In Progress
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>
                  {issueMetrics.inProgress}
                </div>
                <p className='text-xs text-muted-foreground'>
                  Currently being worked on
                </p>
              </CardContent>
            </Card>

            <Card
              className={
                issueMetrics.overdue > 0
                  ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
                  : ''
              }
            >
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium text-muted-foreground'>
                  Overdue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{issueMetrics.overdue}</div>
                <p className='text-xs text-muted-foreground'>Past due date</p>
              </CardContent>
            </Card>
          </div>

          {/* Issues Table */}
          <Card>
            <CardHeader>
              <CardTitle>Project Issues</CardTitle>
            </CardHeader>
            <CardContent>
              {projectIssues.length > 0 ? (
                <DataTable
                  columns={createColumns({
                    onViewIssue: (issue) => {
                      console.log('View issue:', issue);
                    },
                  })}
                  data={projectIssues}
                />
              ) : (
                <div className='text-center py-12'>
                  <div className='text-muted-foreground mb-4'>
                    No issues found for this project
                  </div>
                  <Button>
                    <Plus className='h-4 w-4 mr-2' />
                    Create First Issue
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
