'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { FileText, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useIssues, useStatus, usePriorities } from '@/hooks/use-db';
import type { CreateIssueInput } from '@/lib/supabase/database-modules';
import { IssueRankUtils } from '@/lib/utils/lexorank-utils';
import { extractErrorMessage } from '@/lib/utils/error-utils';

// Zod schema for form validation
const createIssueSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
  description: z.string().optional(),
  status_id: z.string().min(1, 'Status is required'),
  priority_id: z.string().min(1, 'Priority is required'),
});

type CreateIssueFormData = z.infer<typeof createIssueSchema>;

interface CreateIssueDialogProps {
  projectId: string;
  projectName: string;
  trigger?: React.ReactNode;
}

export function CreateIssueDialog({ 
  projectId, 
  projectName, 
  trigger 
}: CreateIssueDialogProps) {
  const [open, setOpen] = useState(false);
  const { addIssue, issues } = useIssues();
  const { status } = useStatus();
  const { priorities } = usePriorities();

  const form = useForm<CreateIssueFormData>({
    resolver: zodResolver(createIssueSchema),
    defaultValues: {
      title: '',
      description: '',
      status_id: '',
      priority_id: '',
    },
  });

  const onSubmit = (data: CreateIssueFormData) => {
    // Generate rank for new issue (add to top of status column)
    const existingIssuesInStatus = issues.filter(
      (issue) => issue.status_id === data.status_id
    );
    const rank = IssueRankUtils.getTopRank(existingIssuesInStatus);

    const issueInput: CreateIssueInput = {
      title: data.title.trim(),
      description: data.description?.trim() || undefined,
      status_id: data.status_id,
      priority_id: data.priority_id,
      project_id: projectId,
      rank,
    };

    addIssue(issueInput)
      .then(() => {
        toast.success('Issue created successfully');
        setOpen(false);
        form.reset();
      })
      .catch((error) => {
        console.error('Failed to create issue:', error);
        const errorMessage = extractErrorMessage(
          error,
          'Failed to create issue. Please try again.'
        );
        toast.error(errorMessage);
      });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant='ghost' size='sm'>
            <FileText className='h-4 w-4 mr-2' />
            New Issue
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Create Issue in {projectName}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter issue title'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Describe the issue...'
                      className='min-h-[80px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='status_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status *</FormLabel>
                    <FormControl>
                      <select
                        className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                        {...field}
                      >
                        <option value=''>Select status</option>
                        {status.map((s) => (
                          <option key={s.id} value={s.id}>
                            {s.name}
                          </option>
                        ))}
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='priority_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority *</FormLabel>
                    <FormControl>
                      <select
                        className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                        {...field}
                      >
                        <option value=''>Select priority</option>
                        {priorities.map((p) => (
                          <option key={p.id} value={p.id}>
                            {p.name}
                          </option>
                        ))}
                      </select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex justify-end gap-2 pt-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? 'Creating...' : 'Create Issue'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
