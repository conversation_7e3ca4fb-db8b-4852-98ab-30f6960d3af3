'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { toast } from 'sonner';

import { DatePicker } from '@/components/issues/date-picker';
import { StatusSelector } from '@/components/issues/status-selector';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useIssues } from '@/hooks/use-db';
import { PriorityIcon } from '@/lib/constants/priorities';
import type { Issue } from '@/lib/supabase/database-modules';

export const columns: ColumnDef<Issue>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      const issue = row.original;
      return (
        <div className='flex flex-col gap-1'>
          <span className='font-medium'>{issue.title}</span>
          {issue.description && (
            <span className='text-sm text-muted-foreground line-clamp-2'>
              {issue.description}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'project',
    header: 'Project',
    cell: ({ row }) => {
      const issue = row.original;
      const project = issue.project;

      if (!project) {
        return <span className='text-muted-foreground'>No project</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='font-medium'>{project.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const issue = row.original;

      return (
        <StatusSelector status={issue.status || null} issueId={issue.id} />
      );
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const issue = row.original;
      const priority = issue.priority;

      if (!priority) {
        return <span className='text-muted-foreground'>No priority set</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          {priority.icon_name && (
            <PriorityIcon PriorityName={priority.icon_name} />
          )}
          <span className='text-sm font-medium'>{priority.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'assignee',
    header: 'Assignee',
    cell: ({ row }) => {
      const issue = row.original;
      const assignee = issue.assignee;

      if (!assignee) {
        return <span className='text-muted-foreground'>Unassigned</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <Avatar className='size-6'>
            <AvatarImage
              src={assignee.avatar_url || undefined}
              alt={assignee.name || assignee.email}
            />
            <AvatarFallback>
              {assignee.email[0] || 'UN'.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm font-medium'>{assignee.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const issue = row.original;
      const { updateIssue } = useIssues();

      const handleDateChange = (date: Date | null) => {
        const dateString = date ? date.toISOString() : null;
        updateIssue(issue.id, { due_date: dateString })
          .then(() => {
            toast.success('Issue due date updated');
          })
          .catch((error) => {
            toast.error('Failed to update issue due date');
            console.error('Error updating issue due date:', error);
          });
      };

      return (
        <DatePicker
          date={issue.due_date ? new Date(issue.due_date) : null}
          onDateChange={handleDateChange}
        />
      );
    },
  },
  {
    accessorKey: 'created_by',
    header: 'Created By',
    cell: ({ row }) => {
      const issue = row.original;
      const creator = issue.created_by;

      if (!creator) {
        return <span className='text-muted-foreground'>Unknown</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='text-sm'>{creator.full_name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const issue = row.original;
      const createdDate = new Date(issue.created_at);

      return (
        <span className='text-sm text-muted-foreground'>
          {createdDate.toLocaleDateString()}
        </span>
      );
    },
  },
];
