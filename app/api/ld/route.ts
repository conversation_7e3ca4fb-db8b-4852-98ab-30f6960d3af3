import { AcceptedApplication } from '@/lib/emails/affiliates';
import LaunchDay from '@/lib/emails/launch-day';
import { Database } from '@/lib/supabase/database-types';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

type wd = Database['public']['Tables']['waitlists']['Row'];

export async function POST(request: Request) {
  const form: wd = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: [form.email_address!],
      bcc: ['<EMAIL>', '<EMAIL>'],
      subject: `The Wait is Over – We're Live!.`,
      text: '',
      react: LaunchDay(form),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
