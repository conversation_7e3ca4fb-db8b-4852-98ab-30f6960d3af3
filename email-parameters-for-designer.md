# Email Parameters Documentation for Designer

This document contains all the parameters used in the email templates in `lib/emails/` directory. Each email template receives specific data that should be considered when designing the email layouts.

## Common Elements Across All Emails

### Base URL
- `baseUrl`: `'https://www.thehuefactory.co/'`

### Color Palette
```javascript
const colors = {
  '100': '#ff4200',  // Primary orange
  '200': '#d53700',  // Darker orange
  '300': '#7f2100',  // Dark orange
  '400': '#3e1000',  // Very dark orange
  '50': '#f8e1db',   // Light orange background (used in some emails)
};
```

### Common Images
- Header image: `${baseUrl}/email_hero.jpg` or `${baseUrl}/thehuefactory_hero.png`
- Logo: `${baseUrl}/Logo_3dicon_orange.png` (42x42px)

### Common Links
- Main website: `https://www.thehuefactory.co/`
- Contact page: `https://www.thehuefactory.co/contact`
- Team dashboard: `https://team.thehuefactory.co/dashboard`

## Email Templates and Their Parameters

### 1. Launch Day Email (`launch-day.tsx`)
**Parameters:**
- `full_name`: string | null

**Usage:**
- Greeting: "Hey {full_name},"
- Announces website launch to waitlist subscribers

---

### 2. Application Not Accepted (`not-accepted.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'

**Usage:**
- Greeting: "Hey {full_name},"
- References the role they applied for

---

### 3. Affiliate Emails (`affiliates/`)

#### Accepted Application (`affiliates/accepted-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
- `email`: string | null
- `pass`: string (temporary password)

**Usage:**
- Greeting: "Hey {full_name},"
- Role confirmation: "Application as a {join_role} has been approved!"
- Login credentials displayed in code blocks

#### Reviewed Application (`affiliates/reviewed-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'

**Usage:**
- Greeting: "Hey {full_name},"
- Acknowledgment of application review

---

### 4. Collaborator Emails (`collaborators/`)

#### Accepted Application (`collaborators/accepted-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
- `email`: string | null
- `pass`: string (temporary password)

**Usage:**
- Same structure as affiliate accepted application

#### Reviewed Application (`collaborators/reviewed-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'

**Usage:**
- Same structure as affiliate reviewed application

---

### 5. Volunteer Emails (`volunteers/`)

#### Accepted Application (`volunteers/accepted-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
- `email`: string | null
- `pass`: string (temporary password)

**Usage:**
- Same structure as other accepted applications

#### Reviewed Application (`volunteers/reviewed-application.tsx`)
**Parameters:**
- `full_name`: string | null
- `join_role`: 'Admin' | 'Collaborator' | 'Affiliate' | 'Volunteer'
- `user_id`: string

**Usage:**
- Similar to other reviewed applications but includes user_id

---

### 6. Issue-Related Emails (`issues/`)

#### Issue Created (`issues/created.tsx`)
**Parameters:**
- `issue`: Object containing:
  - `id`: string
  - `title`: string
  - `description`: string (optional)
  - `status`: Object with `name`, `color` (optional)
  - `priority`: Object with `name`, `icon_name` (optional)
  - `assignee`: User object (optional)
  - `due_date`: string (optional)
- `project`: Object containing:
  - `id`: string
  - `name`: string
  - `description`: string (optional)
- `triggeredBy`: User object with `full_name`, `email`

#### Issue Assigned (`issues/assigned.tsx`)
**Parameters:**
- Same as Issue Created, plus:
- `oldAssignee`: User object (optional)
- `newAssignee`: User object (optional)

#### Issue Status Updated (`issues/status-updated.tsx`)
**Parameters:**
- Same as Issue Created, plus:
- `oldStatus`: Object with `name`, `color`
- `newStatus`: Object with `name`, `color`

#### Issue Priority Changed (`issues/priority-changed.tsx`)
**Parameters:**
- Same as Issue Created, plus:
- `oldPriority`: Object with `name`, `icon_name`
- `newPriority`: Object with `name`, `icon_name`

#### Issue Health Updated (`issues/health-updated.tsx`)
**Parameters:**
- Same as Issue Created, plus:
- `oldHealth`: Object with `name`, `color`, `description`
- `newHealth`: Object with `name`, `color`, `description`

---

### 7. Project-Related Emails (`projects/`)

#### Project Member Added (`projects/member-added.tsx`)
**Parameters:**
- `project`: Object containing:
  - `id`: string
  - `name`: string
  - `description`: string (optional)
  - `status`: Status object (optional)
  - `priority`: Priority object (optional)
  - `health`: Health object (optional)
  - `lead`: User object (optional)
  - `start_date`: string (optional)
  - `target_date`: string (optional)
  - `percent_complete`: number (optional)
- `member`: User object
- `triggeredBy`: User object
- `role`: string

#### Project Status Updated (`projects/status-updated.tsx`)
**Parameters:**
- Same as Project Member Added, plus:
- `oldStatus`: Status object
- `newStatus`: Status object

#### Project Priority Changed (`projects/priority-changed.tsx`)
**Parameters:**
- Same as Project Member Added, plus:
- `oldPriority`: Priority object
- `newPriority`: Priority object

#### Project Health Updated (`projects/health-updated.tsx`)
**Parameters:**
- Same as Project Member Added, plus:
- `oldHealth`: Health object
- `newHealth`: Health object

---

### 8. Proposal-Related Emails (`proposals/`)

#### Proposal Approved (`proposals/approved.tsx`)
**Parameters:**
- `proposal`: Object containing:
  - `id`: string
  - `title`: string
  - `description`: string (optional)
  - `client_name`: string (optional)
  - `client_email`: string (optional)
  - `budget`: number (optional)
  - `timeline`: string (optional)
  - `status`: string (optional)
  - `is_approved`: boolean | null (optional)
- `affiliate`: User object (optional)
- `triggeredBy`: User object
- `approvalDate`: string
- `nextSteps`: string (optional)

#### Proposal Rejected (`proposals/rejected.tsx`)
**Parameters:**
- Same as Proposal Approved, but with:
- `rejectionDate`: string
- `reason`: string (optional)

#### Proposal Status Updated (`proposals/status-updated.tsx`)
**Parameters:**
- Same as Proposal Approved, plus:
- `oldStatus`: string
- `newStatus`: string

---

### 9. Team-Related Emails (`teams/`)

#### Team Member Added (`teams/member-added.tsx`)
**Parameters:**
- `team`: Object containing:
  - `id`: string
  - `name`: string
  - `description`: string (optional)
  - `icon`: string (optional)
  - `color`: string (optional)
- `member`: User object
- `triggeredBy`: User object
- `role`: string (optional)

#### Team Member Removed (`teams/member-removed.tsx`)
**Parameters:**
- Same as Team Member Added, plus:
- `reason`: string (optional)

#### Team Role Changed (`teams/role-changed.tsx`)
**Parameters:**
- Same as Team Member Added, plus:
- `oldRole`: string
- `newRole`: string

---

## Common Data Types

### User Object
```typescript
{
  id: string
  full_name: string
  email: string
  avatar_url?: string
  role?: string
}
```

### Status Object
```typescript
{
  id: string
  name: string
  color: string
  icon_name?: string
}
```

### Priority Object
```typescript
{
  id: string
  name: string
  icon_name?: string
  sort_order?: number
}
```

### Health Object
```typescript
{
  id: string
  name: string
  color: string
  description?: string
}
```

## Design Considerations

1. **Responsive Design**: All emails should work on mobile and desktop
2. **Brand Consistency**: Use the thehuefactory color palette and fonts
3. **Accessibility**: Ensure good contrast ratios and readable fonts
4. **Dynamic Content**: Design should accommodate optional fields gracefully
5. **Action Buttons**: Important CTAs should be prominent and accessible
6. **Footer**: All emails include company branding and copyright notice

## Email Categories by Visual Style

1. **Application Emails**: Use lighter background (`colors['50']`) with `email_hero.jpg`
2. **System Notification Emails**: Use primary orange background (`colors['100']`) with `thehuefactory_hero.png`
3. **Status Update Emails**: Include before/after comparison sections with arrows (→)
4. **Welcome/Acceptance Emails**: Include login credentials in styled code blocks
