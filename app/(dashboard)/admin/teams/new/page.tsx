'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { ArrowLeft, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useTeams, useTeamMembers } from '@/hooks/use-db';
import { TeamMembersSelector } from '@/components/teams/team-members-selector';

// Team creation schema
const createTeamSchema = z.object({
  name: z
    .string()
    .min(1, 'Team name is required')
    .max(255, 'Team name is too long'),
  description: z.string().optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color')
    .optional(),
  icon: z.string().optional(),
});

type CreateTeamFormData = z.infer<typeof createTeamSchema>;

interface SelectedTeamMember {
  id: string;
  name: string;
  email: string;
  avatar_url: string | null;
  joined: boolean;
}

export default function NewTeamPage() {
  const router = useRouter();
  const { addTeam } = useTeams();
  const { addMember } = useTeamMembers();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState<SelectedTeamMember[]>(
    []
  );

  const form = useForm<CreateTeamFormData>({
    resolver: zodResolver(createTeamSchema),
    defaultValues: {
      name: '',
      description: '',
      color: '#3b82f6',
      icon: '',
    },
  });

  const onSubmit = (data: CreateTeamFormData) => {
    setIsSubmitting(true);

    const loadingToast = toast.loading('Creating team...');

    const teamInput = {
      name: data.name,
      description: data.description || null,
      color: data.color || '#3b82f6',
      icon: data.icon || null,
    };

    addTeam(teamInput)
      .then((newTeam) => {
        // Add team members if any are selected
        const memberPromises = selectedMembers.map((member) =>
          addMember({
            team_id: newTeam.id,
            user_id: member.id,
            joined: member.joined,
          })
        );

        // Wait for all members to be added
        return Promise.all(memberPromises).then(() => newTeam);
      })
      .then((newTeam) => {
        toast.dismiss(loadingToast);
        toast.success(
          `Team "${newTeam.name}" created successfully${
            selectedMembers.length > 0
              ? ` with ${selectedMembers.length} member${
                  selectedMembers.length === 1 ? '' : 's'
                }`
              : ''
          }!`
        );
        router.push('/admin/teams');
      })
      .catch((error) => {
        toast.dismiss(loadingToast);
        console.error('Failed to create team:', error);
        toast.error('Failed to create team. Please try again.');
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <div className='container mx-auto py-6'>
      <div className='mb-6'>
        <Button
          variant='ghost'
          onClick={() => router.back()}
          className='mb-4'
          disabled={isSubmitting}
        >
          <ArrowLeft className='mr-2 h-4 w-4' />
          Back
        </Button>

        <div className='flex items-center gap-3'>
          <div className='p-2 bg-primary/10 rounded-lg'>
            <Users className='h-6 w-6 text-primary' />
          </div>
          <div>
            <h1 className='text-2xl font-bold'>Create New Team</h1>
            <p className='text-muted-foreground'>
              Set up a new team to organize your projects and members
            </p>
          </div>
        </div>
      </div>

      <Card className='max-w-2xl'>
        <CardHeader>
          <CardTitle>Team Details</CardTitle>
          <CardDescription>
            Provide the basic information for your new team and add members
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Name *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Enter team name'
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Choose a descriptive name for your team
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Describe the team purpose and goals'
                        disabled={isSubmitting}
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description of the team's purpose
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='color'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Color</FormLabel>
                    <FormControl>
                      <div className='flex items-center gap-3'>
                        <Input
                          type='color'
                          disabled={isSubmitting}
                          className='w-16 h-10 p-1 border rounded'
                          {...field}
                        />
                        <Input
                          placeholder='#3b82f6'
                          disabled={isSubmitting}
                          className='flex-1'
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Choose a color to represent this team
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='icon'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Icon</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='users, code, design, etc.'
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional icon name for the team
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className='pt-6 border-t'>
                <TeamMembersSelector
                  selectedMembers={selectedMembers}
                  onMembersChange={setSelectedMembers}
                  disabled={isSubmitting}
                />
              </div>

              <div className='flex gap-3 pt-4'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                  className='flex-1'
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  disabled={isSubmitting}
                  className='flex-1'
                >
                  {isSubmitting
                    ? 'Creating...'
                    : `Create Team${selectedMembers.length > 0 ? ` with ${selectedMembers.length} member${selectedMembers.length === 1 ? '' : 's'}` : ''}`}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
