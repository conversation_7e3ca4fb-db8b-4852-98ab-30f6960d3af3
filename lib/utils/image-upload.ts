import { supabaseClient } from '@/lib/supabase/auth/client';
import { createStandardError, logError } from '@/lib/utils/error-utils';

const supabase = supabaseClient;

export interface ImageUploadResult {
  url: string;
  path: string;
}

export interface ImageUploadOptions {
  bucket: string;
  folder?: string;
  maxSizeBytes?: number;
  allowedTypes?: string[];
}

const DEFAULT_OPTIONS: Required<ImageUploadOptions> = {
  bucket: 'avatars',
  folder: 'profile-images',
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
};

/**
 * Validates an image file before upload
 */
export function validateImageFile(
  file: File,
  options: Partial<ImageUploadOptions> = {}
): void {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  // Check file size
  if (file.size > opts.maxSizeBytes) {
    throw createStandardError(
      `File size must be less than ${Math.round(opts.maxSizeBytes / 1024 / 1024)}MB`,
      'VALIDATION_ERROR'
    );
  }

  // Check file type
  if (!opts.allowedTypes.includes(file.type)) {
    throw createStandardError(
      `File type must be one of: ${opts.allowedTypes.join(', ')}`,
      'VALIDATION_ERROR'
    );
  }
}

/**
 * Generates a unique file path for the image
 */
export function generateImagePath(
  userId: string,
  fileName: string,
  folder: string = 'profile-images'
): string {
  const timestamp = Date.now();
  const extension = fileName.split('.').pop();
  const cleanFileName = `${userId}-${timestamp}.${extension}`;
  return `${folder}/${cleanFileName}`;
}

/**
 * Uploads an image to Supabase Storage
 */
export async function uploadImage(
  file: File,
  userId: string,
  options: Partial<ImageUploadOptions> = {}
): Promise<ImageUploadResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  try {
    // Validate the file
    validateImageFile(file, opts);

    // Generate unique file path
    const filePath = generateImagePath(userId, file.name, opts.folder);

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(opts.bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false, // Don't overwrite existing files
      });

    if (error) {
      throw createStandardError(
        `Failed to upload image: ${error.message}`,
        'UPLOAD_ERROR'
      );
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from(opts.bucket)
      .getPublicUrl(data.path);

    return {
      url: urlData.publicUrl,
      path: data.path,
    };
  } catch (err) {
    logError('Error uploading image:', err);
    throw err;
  }
}

/**
 * Deletes an image from Supabase Storage
 */
export async function deleteImage(
  imagePath: string,
  bucket: string = 'avatars'
): Promise<void> {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([imagePath]);

    if (error) {
      // Don't throw error if file doesn't exist
      if (error.message.includes('not found')) {
        logError('Image not found for deletion:', imagePath);
        return;
      }
      
      throw createStandardError(
        `Failed to delete image: ${error.message}`,
        'DELETE_ERROR'
      );
    }
  } catch (err) {
    logError('Error deleting image:', err);
    throw err;
  }
}

/**
 * Extracts the file path from a Supabase Storage URL
 */
export function extractPathFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // Find the bucket name and extract everything after it
    const bucketIndex = pathParts.findIndex(part => part === 'avatars' || part === 'resumes');
    if (bucketIndex === -1) return null;
    
    return pathParts.slice(bucketIndex + 1).join('/');
  } catch {
    return null;
  }
}

/**
 * Updates a user's profile image, automatically deleting the previous one
 */
export async function updateProfileImage(
  file: File,
  userId: string,
  currentImageUrl?: string | null
): Promise<ImageUploadResult> {
  try {
    // Upload new image
    const uploadResult = await uploadImage(file, userId);

    // Delete previous image if it exists
    if (currentImageUrl) {
      const previousPath = extractPathFromUrl(currentImageUrl);
      if (previousPath) {
        // Don't await this - delete in background
        deleteImage(previousPath).catch((err) => {
          logError('Failed to delete previous profile image:', err);
        });
      }
    }

    return uploadResult;
  } catch (err) {
    logError('Error updating profile image:', err);
    throw err;
  }
}

/**
 * Creates a preview URL for a file before upload
 */
export function createFilePreview(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Revokes a file preview URL to free memory
 */
export function revokeFilePreview(url: string): void {
  URL.revokeObjectURL(url);
}
