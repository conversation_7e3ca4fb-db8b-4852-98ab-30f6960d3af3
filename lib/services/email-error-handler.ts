// Advanced error handling and retry logic for email failures
// Following utility object pattern instead of class

interface EmailError {
  id: string;
  type: 'network' | 'validation' | 'rate_limit' | 'server' | 'unknown';
  message: string;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
  emailData: Record<string, unknown>;
  lastAttempt: string;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  retryableErrors: string[];
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2,
  retryableErrors: ['network', 'rate_limit', 'server', 'timeout'],
};

// In-memory error tracking (in production, this should use a database)
const errorQueue: Map<string, EmailError> = new Map();
const retryQueue: Set<string> = new Set();

const emailErrorHandler = {
  // Classify error type based on error message/code
  classifyError: (error: Error | string): EmailError['type'] => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
      return 'network';
    }
    if (lowerMessage.includes('rate limit') || lowerMessage.includes('429')) {
      return 'rate_limit';
    }
    if (lowerMessage.includes('validation') || lowerMessage.includes('400')) {
      return 'validation';
    }
    if (lowerMessage.includes('server') || lowerMessage.includes('500')) {
      return 'server';
    }
    return 'unknown';
  },

  // Calculate delay for retry using exponential backoff
  calculateRetryDelay: (
    retryCount: number,
    config: RetryConfig = defaultRetryConfig
  ): number => {
    const delay = Math.min(
      config.baseDelay * config.backoffMultiplier ** retryCount,
      config.maxDelay
    );

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    return Math.floor(delay + jitter);
  },

  // Check if error is retryable
  isRetryableError: (
    errorType: EmailError['type'],
    config: RetryConfig = defaultRetryConfig
  ): boolean => {
    return config.retryableErrors.includes(errorType);
  },

  // Log email error with structured data
  logEmailError: (
    emailId: string,
    error: Error | string,
    emailData: Record<string, unknown>,
    retryCount: number = 0
  ): Promise<EmailError> => {
    return new Promise((resolve) => {
      const errorType = emailErrorHandler.classifyError(error);
      const errorMessage = typeof error === 'string' ? error : error.message;

      const emailError: EmailError = {
        id: emailId,
        type: errorType,
        message: errorMessage,
        timestamp: new Date().toISOString(),
        retryCount,
        maxRetries: defaultRetryConfig.maxRetries,
        emailData,
        lastAttempt: new Date().toISOString(),
      };

      // Store error for tracking
      errorQueue.set(emailId, emailError);

      // Log to console with structured format
      console.error('Email Error:', {
        id: emailId,
        type: errorType,
        message: errorMessage,
        retryCount,
        timestamp: emailError.timestamp,
        isRetryable: emailErrorHandler.isRetryableError(errorType),
      });

      // Log to external service in production
      if (process.env.NODE_ENV === 'production') {
        // Here you would send to your logging service (e.g., Sentry, LogRocket, etc.)
        // Example: Sentry.captureException(error, { extra: emailError });
      }

      resolve(emailError);
    });
  },

  // Retry email sending with exponential backoff
  retryEmailSend: (
    emailId: string,
    sendFunction: () => Promise<unknown>,
    config: RetryConfig = defaultRetryConfig
  ): Promise<unknown> => {
    return new Promise((resolve, reject) => {
      const emailError = errorQueue.get(emailId);

      if (!emailError) {
        reject(new Error('Email error not found in queue'));
        return;
      }

      if (emailError.retryCount >= config.maxRetries) {
        reject(
          new Error(
            `Max retries (${config.maxRetries}) exceeded for email ${emailId}`
          )
        );
        return;
      }

      if (!emailErrorHandler.isRetryableError(emailError.type, config)) {
        reject(new Error(`Error type '${emailError.type}' is not retryable`));
        return;
      }

      // Add to retry queue to prevent duplicate retries
      if (retryQueue.has(emailId)) {
        reject(new Error(`Email ${emailId} is already being retried`));
        return;
      }

      retryQueue.add(emailId);
      const delay = emailErrorHandler.calculateRetryDelay(
        emailError.retryCount,
        config
      );

      console.log(
        `Retrying email ${emailId} in ${delay}ms (attempt ${emailError.retryCount + 1}/${config.maxRetries})`
      );

      setTimeout(() => {
        sendFunction()
          .then((result) => {
            // Success - remove from queues
            errorQueue.delete(emailId);
            retryQueue.delete(emailId);
            console.log(
              `Email ${emailId} sent successfully on retry ${emailError.retryCount + 1}`
            );
            resolve(result);
          })
          .catch((retryError) => {
            // Update retry count and try again
            emailError.retryCount += 1;
            emailError.lastAttempt = new Date().toISOString();
            errorQueue.set(emailId, emailError);
            retryQueue.delete(emailId);

            if (emailError.retryCount >= config.maxRetries) {
              console.error(
                `Email ${emailId} failed after ${config.maxRetries} retries:`,
                retryError
              );
              reject(retryError);
            } else {
              // Recursive retry
              emailErrorHandler
                .retryEmailSend(emailId, sendFunction, config)
                .then(resolve)
                .catch(reject);
            }
          });
      }, delay);
    });
  },

  // Wrapper for email sending with automatic error handling and retry
  sendEmailWithRetry: (
    emailId: string,
    emailData: Record<string, unknown>,
    sendFunction: () => Promise<unknown>,
    config: RetryConfig = defaultRetryConfig
  ): Promise<unknown> => {
    return new Promise((resolve, reject) => {
      sendFunction()
        .then((result) => {
          // Success on first try
          resolve(result);
        })
        .catch((error) => {
          // Log error and attempt retry if applicable
          emailErrorHandler
            .logEmailError(emailId, error, emailData)
            .then((emailError) => {
              if (emailErrorHandler.isRetryableError(emailError.type, config)) {
                return emailErrorHandler.retryEmailSend(
                  emailId,
                  sendFunction,
                  config
                );
              } else {
                throw new Error(`Non-retryable error: ${emailError.message}`);
              }
            })
            .then(resolve)
            .catch(reject);
        });
    });
  },

  // Get error statistics for monitoring
  getErrorStats: (): Promise<{
    totalErrors: number;
    errorsByType: Record<string, number>;
    activeRetries: number;
    recentErrors: EmailError[];
  }> => {
    return new Promise((resolve) => {
      const errors = Array.from(errorQueue.values());
      const errorsByType: Record<string, number> = {};

      errors.forEach((error) => {
        errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      });

      // Get recent errors (last 24 hours)
      const oneDayAgo = new Date(
        Date.now() - 24 * 60 * 60 * 1000
      ).toISOString();
      const recentErrors = errors.filter(
        (error) => error.timestamp > oneDayAgo
      );

      resolve({
        totalErrors: errors.length,
        errorsByType,
        activeRetries: retryQueue.size,
        recentErrors: recentErrors.slice(0, 10), // Last 10 recent errors
      });
    });
  },

  // Clear old errors from memory (should be called periodically)
  cleanupOldErrors: (maxAge: number = 24 * 60 * 60 * 1000): Promise<number> => {
    return new Promise((resolve) => {
      const cutoffTime = new Date(Date.now() - maxAge).toISOString();
      let cleanedCount = 0;

      for (const [id, error] of errorQueue.entries()) {
        if (error.timestamp < cutoffTime && !retryQueue.has(id)) {
          errorQueue.delete(id);
          cleanedCount++;
        }
      }

      console.log(`Cleaned up ${cleanedCount} old email errors`);
      resolve(cleanedCount);
    });
  },

  // Generate unique email ID for tracking
  generateEmailId: (emailType: string, entityId?: string): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const entity = entityId ? `_${entityId}` : '';
    return `${emailType}${entity}_${timestamp}_${random}`;
  },
};

export { emailErrorHandler };
export type { EmailError, RetryConfig };
