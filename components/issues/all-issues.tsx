'use client';

import { type FC, useMemo } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import { useIssues, useStatus } from '@/hooks/use-db';
import { useFilterStore } from '@/lib/store/filter-store';
import { useSearchStore } from '@/lib/store/search-store';
import { useViewStore } from '@/lib/store/view-store';
import type { Issue } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { GroupIssues } from './group-issues';
import { CustomDragLayer } from './issue-grid';
import { SearchIssues } from './search-issues';

export default function AllIssues() {
  const { isSearchOpen, searchQuery } = useSearchStore();
  const { viewType } = useViewStore();
  const { hasActiveFilters } = useFilterStore();

  const isSearching = isSearchOpen && searchQuery.trim() !== '';
  const isViewTypeGrid = viewType === 'grid';
  const isFiltering = hasActiveFilters();

  return (
    <div className='w-full h-full'>
      {isViewTypeGrid ? (
        <ScrollArea className='w-full h-full'>
          {isSearching ? (
            <SearchIssuesView />
          ) : isFiltering ? (
            <FilteredIssuesView isViewTypeGrid={isViewTypeGrid} />
          ) : (
            <GroupIssuesListView isViewTypeGrid={isViewTypeGrid} />
          )}
          <ScrollBar orientation='horizontal' />
        </ScrollArea>
      ) : isSearching ? (
        <SearchIssuesView />
      ) : isFiltering ? (
        <FilteredIssuesView isViewTypeGrid={isViewTypeGrid} />
      ) : (
        <GroupIssuesListView isViewTypeGrid={isViewTypeGrid} />
      )}
    </div>
  );
}

const SearchIssuesView = () => (
  <div className='px-6 mb-6'>
    <SearchIssues />
  </div>
);

const FilteredIssuesView: FC<{
  isViewTypeGrid: boolean;
}> = ({ isViewTypeGrid = false }) => {
  const { filters } = useFilterStore();
  const {
    filterIssues,
    loading: issuesLoading,
    error: issuesError,
  } = useIssues();
  const {
    status: statusOptions,
    loading: statusLoading,
    error: statusError,
  } = useStatus();

  // Get filtered issues using the hook's filter functionality
  const currentFilteredIssues = useMemo(() => {
    return filterIssues(filters);
  }, [filterIssues, filters]);

  // Group filtered issues by status
  const filteredIssuesByStatus = useMemo(() => {
    const result: Record<string, Issue[]> = {};

    statusOptions.forEach((statusItem) => {
      result[statusItem.id] = currentFilteredIssues.filter(
        (issue: Issue) => issue.status_id === statusItem.id
      );
    });

    return result;
  }, [currentFilteredIssues, statusOptions]);

  // Show loading state
  if (issuesLoading || statusLoading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-muted-foreground'>Loading filtered issues...</div>
      </div>
    );
  }

  // Show error state
  if (issuesError || statusError) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>
          Error: {issuesError || statusError}
        </div>
      </div>
    );
  }

  // Show empty state if no status options
  if (statusOptions.length === 0) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-muted-foreground'>
          No status options found. Please check your database setup.
        </div>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <CustomDragLayer />
      <div
        className={cn(
          isViewTypeGrid &&
            'flex h-full gap-3 px-2 py-2 w-max max-w-[calc(100vw-var(--sidebar-width)-3rem)]'
        )}
      >
        {statusOptions.map((statusItem) => (
          <GroupIssues
            key={statusItem.id}
            status={statusItem}
            issues={filteredIssuesByStatus[statusItem.id] || []}
            count={filteredIssuesByStatus[statusItem.id]?.length || 0}
          />
        ))}
      </div>
    </DndProvider>
  );
};

const GroupIssuesListView: FC<{
  isViewTypeGrid: boolean;
}> = ({ isViewTypeGrid = false }) => {
  const {
    issuesByStatus,
    loading: issuesLoading,
    error: issuesError,
  } = useIssues();
  const {
    status: statusOptions,
    loading: statusLoading,
    error: statusError,
  } = useStatus();

  // Show loading state
  if (issuesLoading || statusLoading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-muted-foreground'>Loading issues...</div>
      </div>
    );
  }

  // Show error state
  if (issuesError || statusError) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>
          Error: {issuesError || statusError}
        </div>
      </div>
    );
  }

  // Show empty state if no status options
  if (statusOptions.length === 0) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-muted-foreground'>
          No status options found. Please check your database setup.
        </div>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <CustomDragLayer />
      <div
        className={cn(
          isViewTypeGrid &&
            'flex h-full gap-3 px-2 py-2 w-max max-w-[calc(100vw-var(--sidebar-width)-2rem)]'
        )}
      >
        {statusOptions.map((statusItem) => (
          <GroupIssues
            key={statusItem.id}
            status={statusItem}
            issues={issuesByStatus[statusItem.id] || []}
            count={issuesByStatus[statusItem.id]?.length || 0}
          />
        ))}
      </div>
    </DndProvider>
  );
};
