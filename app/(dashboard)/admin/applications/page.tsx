'use client';
import { ApplicationsMetrics } from '@/components/applications/applications-metrics';
import { ApplicationsTable } from '@/components/tables/applications/applications-table';
import { useApplications } from '@/hooks/use-db';

export default function Page() {
  const { applications, loading } = useApplications();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Applications</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <ApplicationsMetrics applications={applications} loading={loading} />
        <ApplicationsTable />
      </section>
    </main>
  );
}
