import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProjectHealthUpdate } from '@/lib/emails/projects/health-updated';
import type { ProjectHealthUpdateEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY);

export async function POST(request: Request) {
  return new Promise((resolve, reject) => {
    let emailData: ProjectHealthUpdateEmailData;

    request
      .json()
      .then((data: ProjectHealthUpdateEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.project ||
          !emailData.oldHealth ||
          !emailData.newHealth ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: project, oldHealth, newHealth, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = ProjectHealthUpdate(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProjectHealthUpdate(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Project Health Updated: ${emailData.project.name}`,
          react: ProjectHealthUpdate(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Project health update email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Project Health Updated: ${emailData.project.name}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Project health update email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error:
              error.message || 'Failed to send project health update email',
          })
        );
      });
  });
}
