import { supabaseClient } from '@/lib/supabase/auth/client';
import { emailUtils } from './email-utils';

interface NotificationEvent {
  type: string;
  entityId: string;
  entityData?: Record<string, unknown>;
  triggeredBy?: string;
}

interface Recipient {
  email: string;
  full_name: string;
  role: string;
}

// Notification routing service using object pattern instead of class
const notificationRouting = {
  // Get recipients for specific events using Promise chains
  getRecipientsForEvent: (event: NotificationEvent): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      switch (event.type) {
        case 'issue_created':
          notificationRouting
            .getIssueRecipients(event.entityId)
            .then((recipients) => {
              resolve(recipients);
            })
            .catch((error) => {
              reject(error);
            });
          break;

        case 'issue_status_updated':
        case 'issue_priority_changed':
        case 'issue_assigned':
        case 'issue_health_updated':
          notificationRouting
            .getIssueRecipients(event.entityId)
            .then((recipients) => {
              resolve(recipients);
            })
            .catch((error) => {
              reject(error);
            });
          break;

        case 'project_member_added':
          // For project member addition, notify the added member
          if (event.entityData && 'member' in event.entityData) {
            const memberData = event.entityData.member as { email?: string };
            if (memberData?.email) {
              resolve([memberData.email]);
            } else {
              resolve([]);
            }
          } else {
            resolve([]);
          }
          break;

        case 'project_status_updated':
        case 'project_priority_changed':
        case 'project_health_updated':
          notificationRouting
            .getProjectRecipients(event.entityId)
            .then((recipients) => {
              resolve(recipients);
            })
            .catch((error) => {
              reject(error);
            });
          break;

        case 'proposal_approved':
        case 'proposal_rejected':
        case 'proposal_status_updated':
          notificationRouting
            .getProposalRecipients(event.entityId)
            .then((recipients) => {
              resolve(recipients);
            })
            .catch((error) => {
              reject(error);
            });
          break;

        case 'team_member_added':
        case 'team_member_removed':
        case 'team_role_changed':
          notificationRouting
            .getTeamRecipients(event.entityId)
            .then((recipients) => {
              resolve(recipients);
            })
            .catch((error) => {
              reject(error);
            });
          break;

        default:
          console.warn(`Unknown notification event type: ${event.type}`);
          resolve([]);
      }
    });
  },

  // Get recipients for issue-related notifications using Promise chains
  getIssueRecipients: (issueId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      // First get the issue to find the project
      emailUtils
        .getIssueDetails(issueId)
        .then((issue) => {
          const issueData = issue as { project?: { id?: string } };
          if (!issueData.project?.id) {
            resolve([]);
            return;
          }

          // Get project members
          return emailUtils.getProjectMembers(issueData.project.id);
        })
        .then((projectMembers) => {
          // Also include the assignee if they're not already in project members
          return emailUtils.getIssueDetails(issueId).then((issue) => {
            const issueData = issue as { assignee?: { email?: string } };
            const recipients = Array.isArray(projectMembers)
              ? [...projectMembers]
              : [];
            if (
              issueData.assignee?.email &&
              !recipients.includes(issueData.assignee.email)
            ) {
              recipients.push(issueData.assignee.email);
            }
            return recipients;
          });
        })
        .then((recipients) => {
          resolve(recipients);
        })
        .catch((error) => {
          console.error('Error getting issue recipients:', error);
          reject(error);
        });
    });
  },

  // Get recipients for project-related notifications using Promise chains
  getProjectRecipients: (projectId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      emailUtils
        .getProjectMembers(projectId)
        .then((members) => {
          // Also include project lead if not already in members
          return emailUtils.getProjectDetails(projectId).then((project) => {
            const projectData = project as { lead?: { email?: string } };
            const recipients = Array.isArray(members) ? [...members] : [];
            if (
              projectData.lead?.email &&
              !recipients.includes(projectData.lead.email)
            ) {
              recipients.push(projectData.lead.email);
            }
            return recipients;
          });
        })
        .then((recipients) => {
          resolve(recipients);
        })
        .catch((error) => {
          console.error('Error getting project recipients:', error);
          reject(error);
        });
    });
  },

  // Get recipients for proposal-related notifications using Promise chains
  getProposalRecipients: (proposalId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('affiliate_proposals')
        .select('*')
        .eq('id', parseInt(proposalId, 10))
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching proposal details:', error);
            reject(error);
          } else {
            const recipients: string[] = [];

            // Include user email if available
            if (data.user_email) {
              recipients.push(data.user_email);
            }

            // If we have a user_id, get their profile email as well
            if (data.user_id) {
              supabaseClient
                .from('profiles')
                .select('email')
                .eq('id', data.user_id)
                .single()
                .then(({ data: profileData, error: profileError }) => {
                  if (
                    !profileError &&
                    profileData?.email &&
                    !recipients.includes(profileData.email)
                  ) {
                    recipients.push(profileData.email);
                  }
                  resolve(recipients);
                });
            } else {
              resolve(recipients);
            }
          }
        });
    });
  },

  // Get recipients for team-related notifications using Promise chains
  getTeamRecipients: (teamId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      emailUtils
        .getTeamMembers(teamId)
        .then((members) => {
          resolve(members);
        })
        .catch((error) => {
          console.error('Error getting team recipients:', error);
          reject(error);
        });
    });
  },

  // Filter recipients by user preferences (future enhancement)
  filterByUserPreferences: (
    recipients: string[],
    _eventType: string
  ): Promise<string[]> => {
    return new Promise((resolve) => {
      // For now, return all recipients
      // Future: Check email_preferences table
      resolve(recipients);
    });
  },

  // Get all admin emails for BCC using Promise chains
  getAdminBccList: (): Promise<string[]> => {
    return new Promise((resolve) => {
      emailUtils
        .getAdminEmails()
        .then((adminEmails) => {
          resolve(adminEmails);
        })
        .catch((error) => {
          console.error('Error getting admin BCC list:', error);
          // Fallback to default admin email
          resolve(['<EMAIL>']);
        });
    });
  },

  // Check if user should receive notification based on role and preferences
  shouldNotifyUser: (
    _userEmail: string,
    _eventType: string
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      // For now, notify all users
      // Future: Implement user preference checking
      resolve(true);
    });
  },
};

export { notificationRouting };
export type { NotificationEvent, Recipient };
