import type { 
  Proposal, 
  Client, 
  Budget, 
  Project,
  CreateClientInput,
  CreateBudgetInput,
  CreateProjectInput 
} from '@/lib/supabase/database-modules';

export interface ProposalProcessingResult {
  success: boolean;
  client?: Client;
  project?: Project;
  budget?: Budget;
  errors: string[];
  warnings: string[];
  summary: string;
}

export interface ProposalValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  extractedData: {
    clientData: Partial<CreateClientInput>;
    projectData: Partial<CreateProjectInput>;
    budgetData: Partial<CreateBudgetInput>;
  };
}

/**
 * Comprehensive proposal processing service
 * Handles the complete workflow from proposal approval to client/project/budget creation
 */
export class ProposalProcessingService {
  /**
   * Validate proposal data and extract structured information
   */
  static validateAndExtractProposalData(proposal: Proposal): ProposalValidation {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    const proposalData = proposal.affiliate_proposal || {};
    
    // Extract client data
    const clientData: Partial<CreateClientInput> = {
      name: proposal.client_name || proposalData.client_name || proposalData.company_name,
      email: proposal.client_email || proposalData.client_email || proposalData.contact_email,
      phone: proposal.client_phone || proposalData.client_phone || proposalData.contact_phone,
      description: proposal.client_description || proposalData.client_description,
      company_name: proposalData.company_name,
      industry: proposalData.industry,
      contact_person: proposalData.contact_person,
      created_by: proposal.user_id,
    };

    // Extract project data
    const projectName = proposalData.project_name || proposalData.title || proposalData.service_type;
    const projectData: Partial<CreateProjectInput> = {
      name: projectName,
      description: proposalData.description || proposalData.project_description || proposalData.proposal_message,
      is_proposed: true,
      affiliate_user: proposal.user_id,
    };

    // Extract budget data
    const budgetAmount = proposalData.budget || proposalData.estimated_cost || proposalData.price;
    const budgetData: Partial<CreateBudgetInput> = {
      ActualAmount: budgetAmount,
      CurrentAmount: budgetAmount,
      Currency: proposalData.currency || 'USD',
      Category: proposalData.category || this.inferCategoryFromProposal(proposalData),
      has_affiliate: true,
      affiliateId: proposal.user_id,
      AffiliateCommission: proposalData.commission || (budgetAmount ? budgetAmount * 0.1 : 0),
      StartDate: proposalData.start_date || new Date().toISOString().split('T')[0],
      EndDate: proposalData.end_date || this.calculateDefaultEndDate(proposalData.timeline),
      Notes: `Created from proposal ${proposal.id} - ${proposalData.proposal_type || 'referral'}`,
    };

    // Validation rules
    if (!clientData.name) {
      errors.push('Client name is required');
    }

    if (budgetAmount && budgetAmount <= 0) {
      errors.push('Budget amount must be positive');
    }

    if (budgetData.StartDate && budgetData.EndDate) {
      const startDate = new Date(budgetData.StartDate);
      const endDate = new Date(budgetData.EndDate);
      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    // Warnings for missing optional data
    if (!clientData.email) {
      warnings.push('No client email provided - communication may be limited');
    }

    if (!budgetAmount) {
      warnings.push('No budget information provided - budget tracking will not be available');
    }

    if (!projectName) {
      warnings.push('No project name provided - project will need to be created manually');
    }

    if (!proposalData.timeline && !proposalData.end_date) {
      warnings.push('No timeline provided - using default 90-day project duration');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      extractedData: {
        clientData,
        projectData,
        budgetData,
      },
    };
  }

  /**
   * Process an approved proposal through the complete workflow
   */
  static async processApprovedProposal(
    proposal: Proposal,
    hooks: {
      addClient: (input: CreateClientInput) => Promise<Client>;
      fetchClient: (emailOrId: string) => Promise<Client | null>;
      addProject: (input: CreateProjectInput) => Promise<Project>;
      addBudget: (input: CreateBudgetInput) => Promise<Budget>;
    }
  ): Promise<ProposalProcessingResult> {
    const result: ProposalProcessingResult = {
      success: false,
      errors: [],
      warnings: [],
      summary: '',
    };

    try {
      // Step 1: Validate proposal data
      const validation = this.validateAndExtractProposalData(proposal);
      result.warnings = validation.warnings;

      if (!validation.isValid) {
        result.errors = validation.errors;
        result.summary = `Validation failed: ${validation.errors.join(', ')}`;
        return result;
      }

      const { clientData, projectData, budgetData } = validation.extractedData;

      // Step 2: Create or find existing client
      let client: Client;
      
      if (clientData.email) {
        const existingClient = await hooks.fetchClient(clientData.email);
        if (existingClient) {
          client = existingClient;
          result.warnings.push(`Using existing client: ${client.name}`);
        } else {
          client = await hooks.addClient(clientData as CreateClientInput);
        }
      } else {
        client = await hooks.addClient(clientData as CreateClientInput);
      }

      result.client = client;

      // Step 3: Create project if project data is available
      if (projectData.name) {
        const projectInput: CreateProjectInput = {
          ...projectData,
          client_id: client.id,
        } as CreateProjectInput;

        const project = await hooks.addProject(projectInput);
        result.project = project;

        // Step 4: Create budget if budget data is available
        if (budgetData.ActualAmount && budgetData.ActualAmount > 0) {
          const budgetInput: CreateBudgetInput = {
            ...budgetData,
            ProjectId: project.id,
            ClientId: client.id,
          } as CreateBudgetInput;

          const budget = await hooks.addBudget(budgetInput);
          result.budget = budget;
        }
      }

      // Generate summary
      const summaryParts = [
        `✅ Client: ${client.name}`,
        result.project ? `✅ Project: ${result.project.name}` : '',
        result.budget ? `✅ Budget: ${budgetData.Currency}${result.budget.ActualAmount}` : '',
      ].filter(Boolean);

      result.success = true;
      result.summary = summaryParts.join('\n');

      return result;

    } catch (error) {
      console.error('Error processing approved proposal:', error);
      result.errors.push(
        error instanceof Error ? error.message : 'Unknown processing error'
      );
      result.summary = `Processing failed: ${result.errors.join(', ')}`;
      return result;
    }
  }

  /**
   * Infer project category from proposal data
   */
  private static inferCategoryFromProposal(proposalData: any): 'marketing' | 'development' | 'consulting' | 'operations' | 'other' {
    const text = (
      proposalData.proposal_message || 
      proposalData.description || 
      proposalData.service_type || 
      ''
    ).toLowerCase();

    if (text.includes('marketing') || text.includes('advertising') || text.includes('promotion')) {
      return 'marketing';
    }
    if (text.includes('development') || text.includes('coding') || text.includes('programming') || text.includes('website') || text.includes('app')) {
      return 'development';
    }
    if (text.includes('consulting') || text.includes('advice') || text.includes('strategy') || text.includes('planning')) {
      return 'consulting';
    }
    if (text.includes('operations') || text.includes('management') || text.includes('process')) {
      return 'operations';
    }
    
    return 'other';
  }

  /**
   * Calculate default end date based on timeline
   */
  private static calculateDefaultEndDate(timeline?: string): string {
    const now = new Date();
    let daysToAdd = 90; // Default 90 days

    if (timeline) {
      const timelineText = timeline.toLowerCase();
      if (timelineText.includes('week')) {
        const weeks = this.extractNumber(timelineText) || 4;
        daysToAdd = weeks * 7;
      } else if (timelineText.includes('month')) {
        const months = this.extractNumber(timelineText) || 3;
        daysToAdd = months * 30;
      } else if (timelineText.includes('day')) {
        daysToAdd = this.extractNumber(timelineText) || 90;
      }
    }

    const endDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    return endDate.toISOString().split('T')[0];
  }

  /**
   * Extract number from text
   */
  private static extractNumber(text: string): number | null {
    const match = text.match(/\d+/);
    return match ? parseInt(match[0], 10) : null;
  }

  /**
   * Generate processing report for logging/debugging
   */
  static generateProcessingReport(proposal: Proposal, result: ProposalProcessingResult): string {
    const report = [
      `=== Proposal Processing Report ===`,
      `Proposal ID: ${proposal.id}`,
      `Client: ${proposal.client_name}`,
      `Affiliate: ${proposal.user_email}`,
      `Status: ${result.success ? 'SUCCESS' : 'FAILED'}`,
      ``,
      `Results:`,
      result.client ? `✅ Client Created: ${result.client.name} (${result.client.id})` : '❌ No client created',
      result.project ? `✅ Project Created: ${result.project.name} (${result.project.id})` : '⚠️ No project created',
      result.budget ? `✅ Budget Created: ${result.budget.Currency}${result.budget.ActualAmount} (${result.budget.id})` : '⚠️ No budget created',
      ``,
      result.warnings.length > 0 ? `Warnings:\n${result.warnings.map(w => `⚠️ ${w}`).join('\n')}` : '',
      result.errors.length > 0 ? `Errors:\n${result.errors.map(e => `❌ ${e}`).join('\n')}` : '',
      ``,
      `Summary: ${result.summary}`,
      `=================================`,
    ].filter(Boolean).join('\n');

    return report;
  }
}
