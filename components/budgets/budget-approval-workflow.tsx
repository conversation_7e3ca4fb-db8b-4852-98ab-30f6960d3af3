'use client';

import { <PERSON><PERSON><PERSON>cle, Clock, User, XCircle, AlertTriangle, MessageSquare } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useBudgets, useMembers, useProfile } from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/format-currency';
import { cn } from '@/lib/utils/cn';

interface BudgetApprovalWorkflowProps {
  budget: Budget;
  className?: string;
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case 'draft':
      return {
        icon: Clock,
        color: 'bg-gray-100 text-gray-800',
        bgColor: 'bg-gray-50',
        description: 'Budget is in draft status and pending review',
      };
    case 'approved':
      return {
        icon: CheckCircle,
        color: 'bg-green-100 text-green-800',
        bgColor: 'bg-green-50',
        description: 'Budget has been approved and is active',
      };
    case 'locked':
      return {
        icon: AlertTriangle,
        color: 'bg-yellow-100 text-yellow-800',
        bgColor: 'bg-yellow-50',
        description: 'Budget is locked and cannot be modified',
      };
    case 'spent':
      return {
        icon: XCircle,
        color: 'bg-red-100 text-red-800',
        bgColor: 'bg-red-50',
        description: 'Budget has been fully spent',
      };
    default:
      return {
        icon: Clock,
        color: 'bg-gray-100 text-gray-800',
        bgColor: 'bg-gray-50',
        description: 'Unknown status',
      };
  }
};

export function BudgetApprovalWorkflow({ budget, className }: BudgetApprovalWorkflowProps) {
  const { approveBudget, updateBudget } = useBudgets();
  const { members } = useMembers();
  const { profile } = useProfile();
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedApprover, setSelectedApprover] = useState('');
  const [newStatus, setNewStatus] = useState(budget.Status);
  const [approvalNotes, setApprovalNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const statusConfig = getStatusConfig(budget.Status);
  const StatusIcon = statusConfig.icon;

  // Get admin members who can approve budgets
  const adminMembers = members.filter(m => m.role === 'Admin');
  
  // Get approver details if budget is approved
  const approver = budget.ApprovedBy 
    ? members.find(m => m.id === budget.ApprovedBy)
    : null;

  const canApprove = profile?.role === 'Admin' && budget.Status === 'draft';
  const canChangeStatus = profile?.role === 'Admin' && budget.Status !== 'spent';

  const handleApproval = async () => {
    if (!selectedApprover) {
      toast.error('Please select an approver');
      return;
    }

    setIsSubmitting(true);
    try {
      await approveBudget(budget.id, selectedApprover);
      toast.success('Budget approved successfully');
      setIsApprovalDialogOpen(false);
      setSelectedApprover('');
      setApprovalNotes('');
    } catch (error) {
      console.error('Error approving budget:', error);
      toast.error(
        `Failed to approve budget: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStatusChange = async () => {
    if (newStatus === budget.Status) {
      toast.info('Status is already set to this value');
      return;
    }

    setIsSubmitting(true);
    try {
      await updateBudget(budget.id, { Status: newStatus });
      toast.success(`Budget status updated to ${newStatus}`);
      setIsStatusDialogOpen(false);
    } catch (error) {
      console.error('Error updating budget status:', error);
      toast.error(
        `Failed to update status: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card className={cn(statusConfig.bgColor, className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <StatusIcon className="h-6 w-6" />
            <div>
              <CardTitle className="flex items-center gap-2">
                Budget Approval Status
                <Badge className={statusConfig.color}>
                  {budget.Status.toUpperCase()}
                </Badge>
              </CardTitle>
              <CardDescription>
                {statusConfig.description}
              </CardDescription>
            </div>
          </div>
          <div className="flex gap-2">
            {canApprove && (
              <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Approve Budget
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Approve Budget</DialogTitle>
                    <DialogDescription>
                      Approve this budget to make it active. Once approved, expenses can be tracked against it.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div className="p-4 bg-muted rounded-lg">
                      <div className="text-sm font-medium mb-2">Budget Details</div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Amount: {formatCurrency(budget.ActualAmount, budget.Currency)}</div>
                        <div>Category: {budget.Category}</div>
                        <div>Start: {new Date(budget.StartDate).toLocaleDateString()}</div>
                        <div>End: {new Date(budget.EndDate).toLocaleDateString()}</div>
                      </div>
                    </div>

                    <div>
                      <Label>Approver *</Label>
                      <Select value={selectedApprover} onValueChange={setSelectedApprover}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select approver" />
                        </SelectTrigger>
                        <SelectContent>
                          {adminMembers.map((admin) => (
                            <SelectItem key={admin.id} value={admin.id}>
                              {admin.full_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Approval Notes</Label>
                      <Textarea
                        value={approvalNotes}
                        onChange={(e) => setApprovalNotes(e.target.value)}
                        placeholder="Optional notes about this approval..."
                        className="mt-2"
                        rows={3}
                      />
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button 
                        onClick={handleApproval} 
                        disabled={isSubmitting || !selectedApprover}
                        className="flex-1"
                      >
                        {isSubmitting ? 'Approving...' : 'Approve Budget'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsApprovalDialogOpen(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}

            {canChangeStatus && (
              <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" variant="outline" className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Change Status
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Change Budget Status</DialogTitle>
                    <DialogDescription>
                      Update the budget status. This will affect how the budget can be used.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>New Status *</Label>
                      <Select value={newStatus} onValueChange={setNewStatus}>
                        <SelectTrigger className="mt-2">
                          <SelectValue placeholder="Select new status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="approved">Approved</SelectItem>
                          <SelectItem value="locked">Locked</SelectItem>
                          <SelectItem value="spent">Spent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="p-4 bg-muted rounded-lg">
                      <div className="text-sm font-medium mb-2">Status Descriptions</div>
                      <div className="space-y-1 text-xs text-muted-foreground">
                        <div><strong>Draft:</strong> Budget is being prepared and can be edited</div>
                        <div><strong>Approved:</strong> Budget is active and expenses can be tracked</div>
                        <div><strong>Locked:</strong> Budget cannot be modified but expenses can be added</div>
                        <div><strong>Spent:</strong> Budget is fully utilized and closed</div>
                      </div>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <Button 
                        onClick={handleStatusChange} 
                        disabled={isSubmitting || newStatus === budget.Status}
                        className="flex-1"
                      >
                        {isSubmitting ? 'Updating...' : 'Update Status'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setIsStatusDialogOpen(false)}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Approval Information */}
          {budget.Status === 'approved' && budget.ApprovedBy && budget.ApprovalDate && (
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Approved by:</span>
                  {approver && (
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={approver.avatar_url || ''} />
                        <AvatarFallback className="text-xs">
                          {approver.full_name?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{approver.full_name}</span>
                    </div>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatDate(budget.ApprovalDate)}
                </div>
              </div>
            </div>
          )}

          {/* Budget Timeline */}
          <div className="space-y-3">
            <div className="text-sm font-medium">Budget Timeline</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <div className="text-sm font-medium">Start Date</div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(budget.StartDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <div>
                  <div className="text-sm font-medium">End Date</div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(budget.EndDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Budget Notes */}
          {budget.Notes && (
            <div className="space-y-2">
              <div className="text-sm font-medium flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Notes
              </div>
              <div className="text-sm text-muted-foreground p-3 bg-muted rounded-lg">
                {budget.Notes}
              </div>
            </div>
          )}

          {/* Workflow Actions */}
          {budget.Status === 'draft' && !canApprove && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <Clock className="h-4 w-4" />
                <span className="text-sm font-medium">Pending Approval</span>
              </div>
              <div className="text-xs text-yellow-700 mt-1">
                This budget is waiting for admin approval before it can be used.
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
