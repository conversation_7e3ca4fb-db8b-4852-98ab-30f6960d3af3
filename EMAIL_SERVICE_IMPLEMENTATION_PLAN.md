# Comprehensive Email Service Implementation Plan

## Executive Summary

This document outlines a comprehensive email service implementation plan for the thehuefactory project. The plan includes analysis of the existing email system, identification of integration points, service architecture design, template specifications, and a detailed implementation roadmap.

## Phase 1: Analysis and Planning

### 1.1 Current Email System Analysis

#### Existing Email Infrastructure
- **Email Service**: Uses Resend API with React Email templates
- **Current Templates Location**: `lib/emails/` directory
- **Template Structure**: Role-based organization (affiliates, collaborators, volunteers)
- **Existing Templates**:
  - `affiliates/accepted-application.tsx`
  - `collaborators/accepted-application.tsx` 
  - `volunteers/accepted-application.tsx`
  - `launch-day.tsx`
  - `not-accepted.tsx`

#### Email Template Architecture
- **Header/Footer Pattern**: Consistent branding with thehuefactory logo and styling
- **Color Scheme**: Orange theme (`#ff4200`, `#d53700`, `#7f2100`, `#3e1000`)
- **Base URL**: `https://www.thehuefactory.co/`
- **Styling**: Inline CSS with responsive design
- **Components**: Uses `@react-email/components` library

#### Current Email Triggers
- **Application Approval**: Triggered in `hooks/use-db.ts` line 3366
- **Email Endpoint Pattern**: `/api/${role.toLowerCase()}/approved`
- **Integration Method**: Fetch API calls to role-specific endpoints

### 1.2 Integration Points Analysis

#### Database Operations Requiring Email Notifications

**Team Member Management**:
- Application approval (`updateApplication` in `hooks/use-db.ts`)
- Team member addition (`addMember` in team management)
- Project member assignment (`addProjectMember`)

**Project Management**:
- Project creation (`addProject`)
- Project status updates (`updateProject`)
- Project member additions
- Project lead assignments

**Issue Management**:
- Issue creation (`addIssue`)
- Issue status changes (status selector components)
- Issue priority changes
- Issue assignment changes
- Issue health status updates

**Client Requests/Proposals**:
- Proposal approval/rejection (`updateProposal`)
- Proposal status changes

#### Real-time Subscription Points
- Projects table changes (line 1314 in `hooks/use-db.ts`)
- Issues table changes (line 574 in `hooks/use-db.ts`)
- Applications table changes (line 3414 in `hooks/use-db.ts`)
- Team members table changes (line 2121 in `hooks/use-db.ts`)

### 1.3 User Roles and Email Routing

#### User Roles Identified
- **Admin**: Full access, receives all notification types
- **Collaborator**: Project-focused notifications
- **Affiliate**: Client project and earnings notifications
- **Volunteer**: Task and team-based notifications

#### Email Routing Strategy
- **Admin Notifications**: All system events, member approvals, project updates
- **Collaborator Notifications**: Project assignments, issue updates, status changes
- **Affiliate Notifications**: Client project updates, earnings, proposal status
- **Volunteer Notifications**: Task assignments, team updates, event notifications

## Phase 2: Coding Standards and Architecture

### 2.0 Project Coding Standards

#### Utility Functions Pattern
- **✅ Use**: `const utils = { method: () => {} }` - Regular objects with methods
- **❌ Avoid**: `class Utils { static method() {} }` - Classes with only static methods

#### Component Development Pattern
- **Implementation First**: Build functionality completely before adding imports
- **Import Last**: Add imports only after implementation is complete and working
- **Testing**: Verify functionality before optimizing imports

#### Form Handling Standards
- **React Hook Form**: Use React Hook Form and Form component for all forms
- **Zod Validation**: Always use Zod with React Hook Forms for validation and schema definition
- **No Manual State**: Avoid manual state management for form data

#### Promise Handling Pattern
- **✅ Use**: Promise with resolve/reject pattern using `.then()` chains
- **❌ Avoid**: `async/await` syntax in database hooks and onSubmit functions
- **Consistency**: All database operations should use Promise-based `.then()` chains

#### Email Rendering Enhancement
- **Enhanced Rendering**: Use `@react-email/render` with `pretty` and `plainText` options
- **HTML/Text Output**: Include both `html` and `text` in Resend email calls
- **Template Consistency**: Maintain existing header/footer patterns

## Phase 2: Service Design

### 2.1 Email Service Architecture (Updated)

#### Service Location and Structure
- **API Routes**: Follow existing pattern in `app/api/` directory
- **Template Directory**: Organized by feature (`lib/emails/projects/`, `lib/emails/issues/`, etc.)
- **Type Definitions**: `lib/types/email-types.ts`
- **Utility Functions**: Use object pattern instead of classes

#### Email Utility Service Design
```typescript
// lib/services/email-utils.ts
const emailUtils = {
  // Render email with enhanced formatting
  renderEmail: (template: React.ReactElement) => {
    return new Promise((resolve, reject) => {
      render(template)
        .then((rendered) => {
          return Promise.all([
            pretty(rendered),
            render(template, { plainText: true })
          ]);
        })
        .then(([html, text]) => {
          resolve({ html, text });
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // Get recipients based on project/team membership
  getProjectRecipients: (projectId: string) => {
    return new Promise((resolve, reject) => {
      // Implementation using .then() chains
      supabaseClient
        .from('project_members')
        .select('user_id, profiles(email, full_name)')
        .eq('project_id', projectId)
        .then(({ data, error }) => {
          if (error) {
            reject(error);
          } else {
            const recipients = data?.map(member => member.profiles?.email).filter(Boolean) || [];
            resolve(recipients);
          }
        });
    });
  },

  // Send email using enhanced rendering
  sendEmail: (emailData: EmailData) => {
    return new Promise((resolve, reject) => {
      emailUtils.renderEmail(emailData.template)
        .then(({ html, text }) => {
          return resend.emails.send({
            from: 'Thehuefactory <<EMAIL>>',
            replyTo: '<EMAIL>',
            to: emailData.recipients,
            bcc: ['<EMAIL>'],
            subject: emailData.subject,
            react: emailData.template,
            html: html,
            text: text,
          });
        })
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          console.log('Email send error:', error);
          reject(error);
        });
    });
  }
};

export { emailUtils };
```

#### Integration with Existing System
- **Follows API Route Pattern**: Uses existing `/api/` structure with Promise-based handlers
- **Enhanced Email Rendering**: Implements `@react-email/render` with pretty formatting
- **Promise-based Architecture**: All functions use `.then()` chains instead of async/await
- **Utility Object Pattern**: Uses `const utils = { method: () => {} }` instead of classes

### 2.2 Notification Routing System (Updated)

#### Role-Based Email Distribution Utilities
```typescript
// lib/services/notification-routing.ts
const notificationRouting = {
  // Get recipients for specific events using Promise chains
  getRecipientsForEvent: (eventType: string, entityData: any) => {
    return new Promise((resolve, reject) => {
      switch (eventType) {
        case 'issue_created':
          notificationRouting.getProjectMembers(entityData.project_id)
            .then((members) => {
              resolve(members);
            })
            .catch((error) => {
              reject(error);
            });
          break;
        case 'project_member_added':
          resolve([entityData.member.email]);
          break;
        default:
          resolve([]);
      }
    });
  },

  // Get project members using Promise chains
  getProjectMembers: (projectId: string) => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('project_members')
        .select(`
          profiles!inner(
            email,
            full_name,
            role
          )
        `)
        .eq('project_id', projectId)
        .then(({ data, error }) => {
          if (error) {
            reject(error);
          } else {
            const recipients = data?.map(member => member.profiles?.email).filter(Boolean) || [];
            resolve(recipients);
          }
        });
    });
  },

  // Filter by user preferences (future enhancement)
  filterByUserPreferences: (recipients: string[], eventType: string) => {
    return new Promise((resolve) => {
      // For now, return all recipients
      // Future: Check email_preferences table
      resolve(recipients);
    });
  }
};

export { notificationRouting };
```

#### Event-Driven Architecture
- **Event Triggers**: Direct API calls from database hooks using `.then()` chains
- **Promise-based Processing**: All email operations use Promise chains
- **Error Handling**: Consistent error logging following existing patterns
- **Utility Objects**: All services use object pattern instead of classes

## Phase 3: Template Creation

### 3.1 Email Templates Structure

#### Existing Templates (Already Implemented)
- `lib/emails/affiliates/accepted-application.tsx` ✅
- `lib/emails/affiliates/reviewed-application.tsx` ✅
- `lib/emails/collaborators/accepted-application.tsx` ✅
- `lib/emails/collaborators/reviewed-application.tsx` ✅
- `lib/emails/volunteers/accepted-application.tsx` ✅
- `lib/emails/volunteers/reviewed-application.tsx` ✅
- `lib/emails/launch-day.tsx` ✅
- `lib/emails/not-accepted.tsx` ✅

#### New Templates Required

**Project Management Templates**:
1. **Project Member Added** (`lib/emails/projects/member-added.tsx`)
2. **Project Status Update** (`lib/emails/projects/status-updated.tsx`)
3. **Project Priority Change** (`lib/emails/projects/priority-changed.tsx`)
4. **Project Health Update** (`lib/emails/projects/health-updated.tsx`)

**Issue Management Templates**:
5. **Issue Created** (`lib/emails/issues/created.tsx`)
6. **Issue Status Update** (`lib/emails/issues/status-updated.tsx`)
7. **Issue Priority Change** (`lib/emails/issues/priority-changed.tsx`)
8. **Issue Assignment Change** (`lib/emails/issues/assigned.tsx`)
9. **Issue Health Update** (`lib/emails/issues/health-updated.tsx`)

**Proposal Management Templates**:
10. **Proposal Approved** (`lib/emails/proposals/approved.tsx`)
11. **Proposal Rejected** (`lib/emails/proposals/rejected.tsx`)
12. **Proposal Status Update** (`lib/emails/proposals/status-updated.tsx`)

**Team Management Templates**:
13. **Team Member Added** (`lib/emails/teams/member-added.tsx`)
14. **Team Member Role Change** (`lib/emails/teams/role-changed.tsx`)

#### Template Specifications
- **Header**: Reuse existing thehuefactory header image (`thehuefactory_hero.png`)
- **Footer**: Consistent branding with logo (`Logo_3dicon_orange.png`)
- **Styling**: Match existing color scheme (`#ff4200`, `#d53700`, `#7f2100`, `#3e1000`)
- **Email Configuration**: Follow existing pattern with BCC to admin emails
- **Responsiveness**: Mobile-friendly design using existing responsive patterns

### 3.2 Template Data Structures

#### Issue Notification Data
```typescript
interface IssueEmailData {
  issue: {
    id: string
    title: string
    description?: string
    status: StatusData
    priority: PriorityData
    assignee?: UserData
    project?: ProjectData
  }
  recipient: UserData
  actionType: 'created' | 'status_changed' | 'priority_changed' | 'assigned'
  actionBy: UserData
  timestamp: string
}
```

#### Project Notification Data
```typescript
interface ProjectEmailData {
  project: {
    id: string
    name: string
    description?: string
    status: StatusData
    lead?: UserData
    members?: UserData[]
  }
  recipient: UserData
  actionType: 'created' | 'status_changed' | 'member_added' | 'lead_changed'
  actionBy: UserData
  timestamp: string
}
```

## Phase 4: Updated Implementation Roadmap

### 4.1 Phase 1: API Routes Foundation (Week 1)
1. **Create new API routes following enhanced pattern**
   - Project management routes (`app/api/projects/`) with Promise-based handlers
   - Issue management routes (`app/api/issues/`) using `.then()` chains
   - Proposal management routes (`app/api/proposals/`) with enhanced email rendering
   - Team management routes (`app/api/teams/`) following utility object pattern

2. **Set up email template structure and utilities**
   - Create template directories following existing pattern
   - Implement `emailUtils` object with enhanced rendering methods
   - Define TypeScript interfaces for email data
   - Create `notificationRouting` utility object

3. **Database integration points using Promise chains**
   - Update existing hooks to call new API routes using `.then()` chains
   - Add email triggers to status change handlers with Promise-based error handling
   - Implement recipient determination logic using utility objects

### 4.2 Phase 2: Core Email Templates (Week 2)
1. **Create essential email templates using existing header/footer pattern**
   - `lib/emails/projects/member-added.tsx` - Implement functionality first, then imports
   - `lib/emails/issues/created.tsx` - Follow existing template structure
   - `lib/emails/issues/status-updated.tsx` - Use consistent styling and branding
   - `lib/emails/proposals/approved.tsx` - Maintain thehuefactory color scheme
   - `lib/emails/proposals/rejected.tsx` - Include proper error handling

2. **Integration testing with enhanced rendering**
   - Test API route functionality with Promise-based handlers
   - Verify template rendering with `@react-email/render` and `pretty` formatting
   - Validate email delivery with both HTML and plain text versions
   - Test error handling using `.then()` and `.catch()` chains

### 4.3 Phase 3: Advanced Templates and Features (Week 3)
1. **Complete template suite**
   - Priority change notifications
   - Health status update notifications
   - Assignment change notifications
   - Team member role change notifications

2. **Enhanced integration**
   - Add email triggers to all status selectors
   - Implement recipient filtering based on project membership
   - Add BCC to admin emails following existing pattern

### 4.4 Phase 4: Optimization and Monitoring (Week 4)
1. **Performance optimization**
   - Implement error handling following existing pattern
   - Add retry logic for failed email sends
   - Optimize template rendering performance

2. **Monitoring and maintenance**
   - Add logging following existing console.log pattern
   - Create email delivery tracking
   - Set up monitoring for email failures

## Phase 5: Technical Implementation Details

### 5.1 Service Integration Points

#### Hook Modifications Required
- `useApplications`: Add email triggers for approval events
- `useProjects`: Add email triggers for project updates
- `useIssues`: Add email triggers for issue events
- `useTeamMembers`: Add email triggers for member events

#### Database Trigger Locations
- `hooks/use-db.ts` line 3366: Application approval emails
- `hooks/use-db.ts` line 1097: Project creation emails
- `hooks/use-db.ts` line 587: Issue creation emails
- Component status selectors: Status change emails

### 5.2 Error Handling and Logging

#### Email Delivery Monitoring
- Success/failure tracking
- Retry mechanism for failed sends
- Dead letter queue for persistent failures
- Admin notifications for system issues

#### Logging Strategy
- Structured logging with correlation IDs
- Email content logging (for debugging)
- Performance metrics tracking
- User engagement analytics

### 5.3 Existing API Routes Analysis

#### Current Email API Structure
The project already has a well-established API route pattern for email sending:

**Existing Routes**:
- `app/api/affiliate/approved/route.ts` - Affiliate application approval
- `app/api/affiliate/not-accepted/route.ts` - Affiliate application rejection
- `app/api/affiliate/reviewed/route.ts` - Affiliate application review
- `app/api/collaborator/approved/route.ts` - Collaborator application approval
- `app/api/collaborator/not-accepted/route.ts` - Collaborator application rejection
- `app/api/collaborator/reviewed/route.ts` - Collaborator application review
- `app/api/volunteer/approved/route.ts` - Volunteer application approval
- `app/api/volunteer/not-accepted/route.ts` - Volunteer application rejection
- `app/api/volunteer/reviewed/route.ts` - Volunteer application review
- `app/api/ld/route.ts` - Launch day notifications

#### Current API Route Pattern (Enhanced)
```typescript
// Enhanced pattern with proper email rendering
import { AcceptedApplication } from '@/lib/emails/[role]';
import { Database } from '@/lib/supabase/database-types';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { pretty, render } from '@react-email/render';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

export async function POST(request: Request) {
  const form = await request.json();

  return new Promise((resolve, reject) => {
    render(AcceptedApplication(form))
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(AcceptedApplication(form), { plainText: true })
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: [form.email!],
          bcc: ['<EMAIL>'],
          subject: 'Email Subject',
          react: AcceptedApplication(form),
          html: html,
          text: text,
        });
      })
      .then((data) => {
        resolve(NextResponse.json({ data }));
      })
      .catch((error) => {
        console.log(error);
        reject(NextResponse.json({ error }));
      });
  });
}
```

#### Application Integration Points
**Location**: `hooks/use-db.ts` lines 3361-3372
**Current Implementation**:
```typescript
// Send notification email based on role
const emailData = {
  ...updatedApplication,
  pass: password,
};
const emailEndpoint = `/api/${role.toLowerCase()}/approved`;

fetch(emailEndpoint, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(emailData),
})
```

**Status**: ✅ Already implemented and working

### 5.4 New API Routes Required

Following the established pattern, we need to create new API routes for team management notifications:

#### Project Management Routes
```
app/api/projects/
├── member-added/route.ts          # Team member added to project
├── status-updated/route.ts        # Project status change
├── priority-changed/route.ts      # Project priority change
└── health-updated/route.ts        # Project health status change
```

#### Issue Management Routes
```
app/api/issues/
├── created/route.ts               # New issue created
├── status-updated/route.ts        # Issue status change
├── priority-changed/route.ts      # Issue priority change
├── assigned/route.ts              # Issue assignment change
└── health-updated/route.ts        # Issue health status change
```

#### Proposal Management Routes
```
app/api/proposals/
├── approved/route.ts              # Proposal approved
├── rejected/route.ts              # Proposal rejected
└── status-updated/route.ts        # Proposal status change
```

#### Team Management Routes
```
app/api/teams/
├── member-added/route.ts          # New team member added
├── member-removed/route.ts        # Team member removed
└── role-changed/route.ts          # Member role change
```

### 5.5 New API Route Implementation Examples

#### Project Member Added Route (Enhanced)
**File**: `app/api/projects/member-added/route.ts`
```typescript
import { ProjectMemberAdded } from '@/lib/emails/projects/member-added';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { pretty, render } from '@react-email/render';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

type ProjectMemberData = {
  project: {
    id: string;
    name: string;
    description?: string;
  };
  member: {
    id: string;
    full_name: string;
    email: string;
  };
  addedBy: {
    full_name: string;
  };
  role: string;
};

export async function POST(request: Request) {
  const data: ProjectMemberData = await request.json();

  return new Promise((resolve, reject) => {
    render(ProjectMemberAdded(data))
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProjectMemberAdded(data), { plainText: true })
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: [data.member.email],
          bcc: ['<EMAIL>'],
          subject: `You've been added to ${data.project.name}`,
          react: ProjectMemberAdded(data),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        resolve(NextResponse.json({ data: emailResult }));
      })
      .catch((error) => {
        console.log(error);
        reject(NextResponse.json({ error }));
      });
  });
}
```

#### Issue Created Route (Enhanced)
**File**: `app/api/issues/created/route.ts`
```typescript
import { IssueCreated } from '@/lib/emails/issues/created';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { pretty, render } from '@react-email/render';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY!);

type IssueCreatedData = {
  issue: {
    id: string;
    title: string;
    description?: string;
  };
  project: {
    name: string;
  };
  assignee?: {
    full_name: string;
    email: string;
  };
  createdBy: {
    full_name: string;
  };
  recipients: string[]; // Array of email addresses
};

export async function POST(request: Request) {
  const data: IssueCreatedData = await request.json();

  return new Promise((resolve, reject) => {
    render(IssueCreated(data))
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(IssueCreated(data), { plainText: true })
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: data.recipients,
          bcc: ['<EMAIL>'],
          subject: `New Issue: ${data.issue.title}`,
          react: IssueCreated(data),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        resolve(NextResponse.json({ data: emailResult }));
      })
      .catch((error) => {
        console.log(error);
        reject(NextResponse.json({ error }));
      });
  });
}
```

### 5.6 Integration Points for New Email Notifications

#### Issue Creation Integration
**Location**: `hooks/use-db.ts` lines 584-588
**Current Code**:
```typescript
if (payload.eventType === 'INSERT' && payload.new) {
  const issue = payload.new as Issue;
  notificationService.issueCreated(issue.title);
}
```

**Enhancement Required**: Add email notification alongside in-app notification using Promise chains
```typescript
if (payload.eventType === 'INSERT' && payload.new) {
  const issue = payload.new as Issue;
  notificationService.issueCreated(issue.title);

  // Add email notification using Promise chain
  notificationRouting.getRecipientsForEvent('issue_created', { project_id: issue.project_id })
    .then((recipients) => {
      return fetch('/api/issues/created', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          issue: payload.new,
          recipients: recipients,
          createdBy: currentUser
        }),
      });
    })
    .then((response) => {
      console.log('Issue creation email sent successfully');
    })
    .catch((error) => {
      console.error('Failed to send issue creation email:', error);
    });
}
```

#### Issue Status Updates Integration
**Locations**: Multiple status selector components
- `components/issues/status-selector.tsx` lines 69-77
- `components/tables/issues/columns.tsx` lines 62-71
- `components/issues/issue-context-menu.tsx` lines 98-110

**Enhancement Required**: Add email notifications to status change handlers using Promise chains
```typescript
// In status change handlers - following Promise chain pattern
updateIssue(issueId, { status_id: statusId })
  .then(() => {
    toast.success(`Status updated to ${newStatus.name}`);

    // Get recipients and send email notification using Promise chain
    return notificationRouting.getProjectMembers(issueData.project_id);
  })
  .then((recipients) => {
    return fetch('/api/issues/status-updated', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        issue: issueData,
        oldStatus: previousStatus,
        newStatus: newStatus,
        updatedBy: currentUser,
        recipients: recipients
      }),
    });
  })
  .then(() => {
    console.log('Status update email sent successfully');
  })
  .catch((error) => {
    console.error('Failed to send status update email:', error);
  });
```

#### Project Member Addition Integration
**Location**: `hooks/use-db.ts` lines 2015-2025 (addMember function)
**Enhancement Required**: Add email notification when team members are added using Promise chains
```typescript
// In addMember function after successful database insert - following Promise pattern
supabaseClient
  .from('team_members')
  .insert([optimisticMember])
  .then(({ data, error }) => {
    if (error) {
      throw error;
    }
    return data;
  })
  .then((data) => {
    // Send email notification using Promise chain
    return fetch('/api/projects/member-added', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        project: projectData,
        member: memberData,
        addedBy: currentUser,
        role: memberRole
      }),
    });
  })
  .then(() => {
    console.log('Project member added email sent successfully');
  })
  .catch((error) => {
    console.error('Failed to send project member added email:', error);
  });
```

#### Proposal Status Updates Integration
**Location**: `components/tables/proposals/proposals-table.tsx` lines 25-44
**Enhancement Required**: Add email notifications for proposal status changes
```typescript
const handleApprovalChange = (proposalId: number, approved: boolean | null) => {
  const updatePromise = updateProposal(proposalId, { is_approved: approved });

  updatePromise.then(() => {
    // Add email notification
    const endpoint = approved === true ? '/api/proposals/approved' :
                    approved === false ? '/api/proposals/rejected' :
                    '/api/proposals/status-updated';

    fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        proposal: proposalData,
        status: approved,
        updatedBy: currentUser
      }),
    });
  });

  // Existing toast notification code...
};
```

### 5.7 Email Template Data Types

#### Project Member Added Email Data
```typescript
interface ProjectMemberEmailData {
  project: {
    id: string;
    name: string;
    description?: string;
    lead?: {
      full_name: string;
    };
  };
  member: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  };
  addedBy: {
    full_name: string;
  };
  role: string;
  timestamp: string;
}
```

#### Issue Created Email Data
```typescript
interface IssueCreatedEmailData {
  issue: {
    id: string;
    title: string;
    description?: string;
    priority?: {
      name: string;
      icon_name: string;
    };
    status?: {
      name: string;
      color: string;
    };
  };
  project: {
    id: string;
    name: string;
  };
  assignee?: {
    full_name: string;
    email: string;
  };
  createdBy: {
    full_name: string;
  };
  timestamp: string;
}
```

#### Status Update Email Data
```typescript
interface StatusUpdateEmailData {
  entity: 'issue' | 'project';
  item: {
    id: string;
    title: string;
    name?: string;
  };
  oldStatus: {
    name: string;
    color: string;
  };
  newStatus: {
    name: string;
    color: string;
  };
  updatedBy: {
    full_name: string;
  };
  project?: {
    name: string;
  };
  timestamp: string;
}
```

### 5.8 Database Schema Considerations

#### Email Preferences Table (Recommended Addition)
```sql
CREATE TABLE email_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL,
  enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, notification_type)
);
```

#### Email Log Table (Recommended Addition)
```sql
CREATE TABLE email_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  recipient_email TEXT NOT NULL,
  subject TEXT NOT NULL,
  template_name TEXT NOT NULL,
  status TEXT CHECK (status IN ('sent', 'failed', 'pending')) DEFAULT 'pending',
  error_message TEXT,
  sent_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Phase 6: Testing Strategy

### 6.1 Unit Testing
- Email template rendering tests
- Service method functionality tests
- Data transformation tests
- Error handling tests

### 6.2 Integration Testing
- End-to-end email delivery tests
- Database trigger tests
- Role-based routing tests
- Template data binding tests

### 6.3 User Acceptance Testing
- Email content validation
- Delivery timing tests
- Mobile responsiveness tests
- Cross-client compatibility tests

## Phase 7: Deployment and Monitoring

### 7.1 Deployment Strategy
- Feature flag implementation
- Gradual rollout by user role
- A/B testing for template effectiveness
- Rollback procedures

### 7.2 Monitoring and Alerting
- Email delivery rate monitoring
- Template rendering error alerts
- Service performance metrics
- User engagement tracking

### 7.3 Maintenance Procedures
- Template update procedures
- Service scaling guidelines
- Error investigation workflows
- Performance optimization cycles

## Conclusion

This comprehensive email service implementation will provide a robust, scalable notification system that enhances user engagement and keeps team members informed of important project activities. The phased approach ensures minimal disruption to existing functionality while building a foundation for future email marketing and communication features.

The implementation leverages existing infrastructure while introducing modern email service patterns that will support the growing needs of the thehuefactory platform.

## Next Steps

1. **Review and Approval**: Stakeholder review of this implementation plan
2. **Resource Allocation**: Assign development resources and timeline
3. **Environment Setup**: Prepare development and testing environments
4. **Phase 1 Kickoff**: Begin foundation implementation
5. **Regular Reviews**: Weekly progress reviews and plan adjustments
