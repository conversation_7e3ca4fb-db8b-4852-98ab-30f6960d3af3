'use client';

import { Grid3X3, List } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useViewStore } from '@/lib/store/view-store';
import { cn } from '@/lib/utils';

export function ViewToggle() {
  const { viewType, setViewType } = useViewStore();

  return (
    <div className="flex items-center gap-1 border rounded-md p-1">
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'h-7 w-7 p-0',
          viewType === 'list' && 'bg-muted'
        )}
        onClick={() => setViewType('list')}
        title="List view"
      >
        <List className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'h-7 w-7 p-0',
          viewType === 'grid' && 'bg-muted'
        )}
        onClick={() => setViewType('grid')}
        title="Grid view"
      >
        <Grid3X3 className="h-4 w-4" />
      </Button>
    </div>
  );
}
