'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Edit, Save, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';
import { toast } from 'sonner';
import { MemberStatusDisplay } from '@/components/members/member-status-selector';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  useIssues,
  useMembers,
  useProjects,
  useProposals,
} from '@/hooks/use-db';
import type {
  Member,
  UpdateMemberInput,
} from '@/lib/supabase/database-modules';

interface MemberDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function MemberDetailPage({ params }: MemberDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { members, loading, updateMember } = useMembers();
  const { projects } = useProjects();
  const { issues } = useIssues();
  const { proposals } = useProposals();

  const [member, setMember] = useState<Member | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<UpdateMemberInput>({});
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (!loading && members.length > 0) {
      const foundMember = members.find((m) => m.id === resolvedParams.id);
      if (foundMember) {
        setMember(foundMember);
        setEditForm({
          name: foundMember.name,
          email: foundMember.email,
          full_name: foundMember.full_name,
          username: foundMember.username,
          role: foundMember.role,
        });
      }
    }
  }, [members, loading, resolvedParams.id]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (member) {
      setEditForm({
        name: member.name,
        email: member.email,
        full_name: member.full_name,
        username: member.username,
        role: member.role,
      });
    }
  };

  const handleSave = () => {
    if (!member) return;

    setIsUpdating(true);
    const memberName = member.full_name || member.name;
    const updatePromise = updateMember(member.id, editForm)
      .then((updatedMember) => {
        setMember(updatedMember);
        setIsEditing(false);
        return updatedMember;
      })
      .finally(() => {
        setIsUpdating(false);
      });

    toast.promise(updatePromise, {
      loading: `Updating ${memberName}'s profile...`,
      success: `${memberName}'s profile updated successfully`,
      error: (error) =>
        `Failed to update profile: ${error?.message || 'Unknown error'}`,
    });
  };

  const handleInputChange = (field: keyof UpdateMemberInput, value: string) => {
    setEditForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return <MemberDetailSkeleton />;
  }

  if (!member) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Member Not Found</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-destructive'>Member not found</div>
          </div>
        </section>
      </main>
    );
  }

  const displayName = member.full_name || member.name;
  const initials = displayName
    .split(' ')
    .map((n) => n[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.back()}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>{displayName}</p>
        </div>
        <div className='flex items-center gap-2'>
          {isEditing ? (
            <>
              <Button
                variant='outline'
                size='sm'
                onClick={handleCancelEdit}
                disabled={isUpdating}
              >
                <X className='h-4 w-4 mr-2' />
                Cancel
              </Button>
              <Button size='sm' onClick={handleSave} disabled={isUpdating}>
                <Save className='h-4 w-4 mr-2' />
                {isUpdating ? 'Saving...' : 'Save'}
              </Button>
            </>
          ) : (
            <Button size='sm' onClick={handleEdit}>
              <Edit className='h-4 w-4 mr-2' />
              Edit
            </Button>
          )}
        </div>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <Card>
          <CardHeader>
            <CardTitle>Personal Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Avatar className='h-16 w-16'>
                <AvatarImage src={member.avatar_url || ''} alt={displayName} />
                <AvatarFallback className='text-lg'>{initials}</AvatarFallback>
              </Avatar>
              <div className='flex-1 space-y-2'>
                <div className='flex items-center gap-4'>
                  <MemberStatusDisplay member={member} />
                  <Badge
                    variant={
                      member.role === 'Admin' ? 'destructive' : 'default'
                    }
                  >
                    {member.role}
                  </Badge>
                </div>
                {member.joined_date && (
                  <p className='text-sm text-muted-foreground'>
                    Joined {new Date(member.joined_date).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Display Name</Label>
                {isEditing ? (
                  <Input
                    id='name'
                    value={editForm.name || ''}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                ) : (
                  <p className='text-sm'>{member.name}</p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='full_name'>Full Name</Label>
                {isEditing ? (
                  <Input
                    id='full_name'
                    value={editForm.full_name || ''}
                    onChange={(e) =>
                      handleInputChange('full_name', e.target.value)
                    }
                  />
                ) : (
                  <p className='text-sm'>
                    {member.full_name || 'Not provided'}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='email'>Email</Label>
                {isEditing ? (
                  <Input
                    id='email'
                    type='email'
                    value={editForm.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                ) : (
                  <p className='text-sm'>{member.email}</p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='username'>Username</Label>
                {isEditing ? (
                  <Input
                    id='username'
                    value={editForm.username || ''}
                    onChange={(e) =>
                      handleInputChange('username', e.target.value)
                    }
                    placeholder='@username'
                  />
                ) : (
                  <p className='text-sm'>
                    {member.username ? `@${member.username}` : 'Not set'}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='role'>Role</Label>
                {isEditing ? (
                  <Select
                    value={editForm.role}
                    onValueChange={(value) => handleInputChange('role', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Admin'>Admin</SelectItem>
                      <SelectItem value='Collaborator'>Collaborator</SelectItem>
                      <SelectItem value='Affiliate'>Affiliate</SelectItem>
                      <SelectItem value='Volunteer'>Volunteer</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <p className='text-sm'>{member.role}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Projects Section */}
        <Card>
          <CardHeader>
            <CardTitle>Projects</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const memberProjects = projects.filter(
                (project) =>
                  project.lead_id === member.id ||
                  project.created_by === member.id
              );

              if (memberProjects.length === 0) {
                return (
                  <p className='text-sm text-muted-foreground'>
                    No projects found for this member.
                  </p>
                );
              }

              return (
                <div className='space-y-3'>
                  {memberProjects.map((project) => (
                    <div
                      key={project.id}
                      className='flex items-center justify-between p-3 border'
                    >
                      <div className='flex-1'>
                        <h4 className='font-medium'>{project.name}</h4>
                        <p className='text-sm text-muted-foreground'>
                          {project.description || 'No description'}
                        </p>
                        <div className='flex items-center gap-2 mt-2'>
                          <Badge variant='outline'>
                            {project.percent_complete}% Complete
                          </Badge>
                          {project.lead_id === member.id && (
                            <Badge variant='secondary'>Project Lead</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              );
            })()}
          </CardContent>
        </Card>

        {/* Issues Section */}
        <Card>
          <CardHeader>
            <CardTitle>Issues & Tasks</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const memberIssues = issues.filter(
                (issue) =>
                  issue.assignee_id === member.id ||
                  issue.created_by === member.id
              );

              if (memberIssues.length === 0) {
                return (
                  <p className='text-sm text-muted-foreground'>
                    No issues or tasks found for this member.
                  </p>
                );
              }

              return (
                <div className='space-y-3'>
                  {memberIssues.slice(0, 5).map((issue) => (
                    <div
                      key={issue.id}
                      className='flex items-center justify-between p-3 border'
                    >
                      <div className='flex-1'>
                        <h4 className='font-medium'>{issue.title}</h4>
                        <p className='text-sm text-muted-foreground'>
                          {issue.identifier} •{' '}
                          {issue.project?.name || 'No project'}
                        </p>
                        <div className='flex items-center gap-2 mt-2'>
                          {issue.status && (
                            <Badge
                              variant='outline'
                              style={{ color: issue.status.color }}
                            >
                              {issue.status.name}
                            </Badge>
                          )}
                          {issue.assignee_id === member.id && (
                            <Badge variant='secondary'>Assigned</Badge>
                          )}
                          {issue.created_by === member.id && (
                            <Badge variant='outline'>Created</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  {memberIssues.length > 5 && (
                    <p className='text-sm text-muted-foreground text-center'>
                      And {memberIssues.length - 5} more issues...
                    </p>
                  )}
                </div>
              );
            })()}
          </CardContent>
        </Card>

        {/* Affiliate Proposals Section - Only visible for Affiliates */}
        {member.role === 'Affiliate' && (
          <Card>
            <CardHeader>
              <CardTitle>Affiliate Proposals</CardTitle>
            </CardHeader>
            <CardContent>
              {(() => {
                const affiliateProposals = proposals.filter(
                  (proposal) => proposal.user_id === member.id
                );

                if (affiliateProposals.length === 0) {
                  return (
                    <p className='text-sm text-muted-foreground'>
                      No proposals found for this affiliate.
                    </p>
                  );
                }

                return (
                  <div className='space-y-3'>
                    {affiliateProposals.map((proposal) => (
                      <div
                        key={proposal.id}
                        className='flex items-center justify-between p-3 border'
                      >
                        <div className='flex-1'>
                          <h4 className='font-medium'>
                            {proposal.affiliate_proposal?.client_name ||
                              'Untitled Proposal'}
                          </h4>
                          <p className='text-sm text-muted-foreground'>
                            {proposal.affiliate_proposal?.proposal_message ||
                              'No description'}
                          </p>
                          <div className='flex items-center gap-2 mt-2'>
                            <Badge variant='outline'>
                              {new Date(
                                proposal.created_at
                              ).toLocaleDateString()}
                            </Badge>
                            {proposal.affiliate_proposal?.proposal_type && (
                              <Badge variant='secondary'>
                                {proposal.affiliate_proposal.proposal_type}
                              </Badge>
                            )}
                            {proposal.is_approved === true && (
                              <Badge variant='default' className='bg-green-500'>
                                Approved
                              </Badge>
                            )}
                            {proposal.is_approved === false && (
                              <Badge variant='destructive'>Rejected</Badge>
                            )}
                            {proposal.is_approved === null && (
                              <Badge variant='secondary'>Pending Review</Badge>
                            )}
                            {proposal.is_recieved === true && (
                              <Badge variant='outline'>Received</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                );
              })()}
            </CardContent>
          </Card>
        )}

        {/* Teams Section */}
        <Card>
          <CardHeader>
            <CardTitle>Team Memberships</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              // Note: This would require a team_members query to get actual memberships
              // For now, showing placeholder since we don't have the team membership data structure
              return (
                <p className='text-sm text-muted-foreground'>
                  Team membership information will be displayed here once team
                  member relationships are available.
                </p>
              );
            })()}
          </CardContent>
        </Card>
      </section>
    </main>
  );
}

function MemberDetailSkeleton() {
  return (
    <main className='h-full flex flex-col'>
      {/* Header */}
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <div className='h-8 w-8 bg-muted animate-pulse' />
          <div className='h-4 w-32 bg-muted  animate-pulse' />
        </div>
        <div className='flex items-center gap-2'>
          <div className='h-8 w-20 bg-muted  animate-pulse' />
          <div className='h-8 w-20 bg-muted  animate-pulse' />
        </div>
      </header>

      <section className='flex-1 p-6 space-y-6'>
        {/* Personal Information Card */}
        <div className='border '>
          <div className='p-6 border-b'>
            <div className='h-6 w-40 bg-muted  animate-pulse' />
          </div>
          <div className='p-6 space-y-6'>
            <div className='flex items-center gap-4'>
              <div className='h-16 w-16 bg-muted animate-pulse' />
              <div className='flex-1 space-y-2'>
                <div className='flex items-center gap-4'>
                  <div className='h-4 w-24 bg-muted  animate-pulse' />
                  <div className='h-4 w-16 bg-muted  animate-pulse' />
                </div>
                <div className='h-4 w-32 bg-muted  animate-pulse' />
              </div>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {Array(5)
                .fill(0)
                .map((_, i) => (
                  <div key={i} className='space-y-2'>
                    <div className='h-4 w-20 bg-muted  animate-pulse' />
                    <div className='h-8 w-full bg-muted  animate-pulse' />
                  </div>
                ))}
            </div>
          </div>
        </div>

        {/* Projects Card */}
        <div className='border '>
          <div className='p-6 border-b'>
            <div className='h-6 w-24 bg-muted  animate-pulse' />
          </div>
          <div className='p-6 space-y-3'>
            {Array(2)
              .fill(0)
              .map((_, i) => (
                <div key={i} className='p-3 border  space-y-2'>
                  <div className='h-5 w-3/4 bg-muted  animate-pulse' />
                  <div className='h-4 w-full bg-muted  animate-pulse' />
                  <div className='flex items-center gap-2'>
                    <div className='h-4 w-16 bg-muted  animate-pulse' />
                    <div className='h-4 w-20 bg-muted  animate-pulse' />
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Issues & Tasks Card */}
        <div className='border '>
          <div className='p-6 border-b'>
            <div className='h-6 w-32 bg-muted  animate-pulse' />
          </div>
          <div className='p-6 space-y-3'>
            {Array(2)
              .fill(0)
              .map((_, i) => (
                <div key={i} className='p-3 border  space-y-2'>
                  <div className='h-5 w-3/4 bg-muted  animate-pulse' />
                  <div className='h-4 w-full bg-muted  animate-pulse' />
                  <div className='flex items-center gap-2'>
                    <div className='h-4 w-16 bg-muted  animate-pulse' />
                    <div className='h-4 w-20 bg-muted  animate-pulse' />
                    <div className='h-4 w-20 bg-muted  animate-pulse' />
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Affiliate Proposals Card */}
        <div className='border '>
          <div className='p-6 border-b'>
            <div className='h-6 w-40 bg-muted  animate-pulse' />
          </div>
          <div className='p-6 space-y-3'>
            {Array(2)
              .fill(0)
              .map((_, i) => (
                <div key={i} className='p-3 border  space-y-2'>
                  <div className='h-5 w-3/4 bg-muted  animate-pulse' />
                  <div className='h-4 w-full bg-muted  animate-pulse' />
                  <div className='flex items-center gap-2'>
                    <div className='h-4 w-16 bg-muted  animate-pulse' />
                    <div className='h-4 w-20 bg-muted  animate-pulse' />
                    <div className='h-4 w-24 bg-muted  animate-pulse' />
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Team Memberships Card */}
        <div className='border '>
          <div className='p-6 border-b'>
            <div className='h-6 w-40 bg-muted  animate-pulse' />
          </div>
          <div className='p-6'>
            <div className='h-4 w-full bg-muted  animate-pulse' />
          </div>
        </div>
      </section>
    </main>
  );
}
