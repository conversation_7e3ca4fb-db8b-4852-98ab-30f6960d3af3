import { Resend } from 'resend';
import { pretty, render } from '@react-email/render';
import { supabaseClient } from '@/lib/supabase/auth/client';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

interface EmailData {
  template: React.ReactElement;
  recipients: string[];
  subject: string;
  bcc?: string[];
}

interface EmailResult {
  success: boolean;
  data?: unknown;
  error?: unknown;
}

interface RenderedEmail {
  html: string;
  text: string;
}

// Email utility service using object pattern instead of class
const emailUtils = {
  // Render email with enhanced formatting using Promise chains
  renderEmail: (template: React.ReactElement): Promise<RenderedEmail> => {
    return new Promise((resolve, reject) => {
      render(template)
        .then((rendered) => {
          return Promise.all([
            pretty(rendered),
            render(template, { plainText: true }),
          ]);
        })
        .then(([html, text]) => {
          resolve({ html, text });
        })
        .catch((error) => {
          console.error('Email rendering error:', error);
          reject(error);
        });
    });
  },

  // Send email using enhanced rendering with Promise chains
  sendEmail: (emailData: EmailData): Promise<EmailResult> => {
    return new Promise((resolve, reject) => {
      emailUtils
        .renderEmail(emailData.template)
        .then(({ html, text }) => {
          return resend.emails.send({
            from: 'Thehuefactory <<EMAIL>>',
            replyTo: '<EMAIL>',
            to: emailData.recipients,
            bcc: emailData.bcc || ['<EMAIL>'],
            subject: emailData.subject,
            react: emailData.template,
            html: html,
            text: text,
          });
        })
        .then((result) => {
          console.log('Email sent successfully:', {
            id: result.data?.id,
            recipients: emailData.recipients,
            subject: emailData.subject,
          });
          resolve({ success: true, data: result });
        })
        .catch((error) => {
          console.error('Email send error:', error);
          reject({ success: false, error });
        });
    });
  },

  // Get project members with their email addresses using Promise chains
  getProjectMembers: (projectId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('project_members')
        .select(
          `
          profiles!inner(
            email,
            full_name,
            role
          )
        `
        )
        .eq('project_id', projectId)
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching project members:', error);
            reject(error);
          } else {
            const recipients =
              data
                ?.map(
                  (member: Record<string, unknown>) =>
                    (member.profiles as { email?: string })?.email
                )
                .filter(Boolean) || [];
            resolve(recipients as string[]);
          }
        });
    });
  },

  // Get team members with their email addresses using Promise chains
  getTeamMembers: (teamId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('team_members')
        .select(
          `
          profiles!inner(
            email,
            full_name,
            role
          )
        `
        )
        .eq('team_id', teamId)
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching team members:', error);
            reject(error);
          } else {
            const recipients =
              data?.map((member) => member.profiles?.email).filter(Boolean) ||
              [];
            resolve(recipients as string[]);
          }
        });
    });
  },

  // Get admin email addresses for notifications using Promise chains
  getAdminEmails: (): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('profiles')
        .select('email')
        .eq('role', 'Admin')
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching admin emails:', error);
            reject(error);
          } else {
            const adminEmails =
              data?.map((admin) => admin.email).filter(Boolean) || [];
            // Always include default admin emails as fallback
            const defaultAdmins = ['<EMAIL>'];
            const allAdmins = [...new Set([...adminEmails, ...defaultAdmins])];
            resolve(allAdmins as string[]);
          }
        });
    });
  },

  // Get user details by ID using Promise chains
  getUserDetails: (
    userId: string
  ): Promise<{
    id: string;
    email?: string;
    full_name?: string | null;
    role?: string;
  }> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('profiles')
        .select('id, full_name, email, avatar_url, role')
        .eq('id', userId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching user details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },

  // Get project details by ID using Promise chains
  getProjectDetails: (projectId: string): Promise<Record<string, unknown>> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('projects')
        .select(
          `
          *,
          lead:profiles!lead_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          )
        `
        )
        .eq('id', projectId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching project details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },

  // Get issue details by ID using Promise chains
  getIssueDetails: (issueId: string): Promise<Record<string, unknown>> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('issues')
        .select(
          `
          *,
          assignee:profiles!assignee_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          ),
          project:projects!project_id(
            id,
            name,
            description
          )
        `
        )
        .eq('id', issueId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching issue details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },
};

export { emailUtils };
export type { EmailData, EmailResult, RenderedEmail };
