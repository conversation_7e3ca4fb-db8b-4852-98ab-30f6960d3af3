'use client';

import { useState } from 'react';
import { Calendar, Hash, Plus, User, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useProjects, useMembers } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';

interface SelectedTeamProject {
  id: string;
  name: string;
  description: string | null;
  status: string;
}

interface TeamProjectsSelectorProps {
  selectedProjects: SelectedTeamProject[];
  onProjectsChange: (projects: SelectedTeamProject[]) => void;
  disabled?: boolean;
}

export function TeamProjectsSelector({
  selectedProjects,
  onProjectsChange,
  disabled = false,
}: TeamProjectsSelectorProps) {
  const { projects } = useProjects();
  const { members } = useMembers();
  const [open, setOpen] = useState(false);

  // Filter out already selected projects
  const availableProjects = projects.filter(
    (project) =>
      !selectedProjects.some((selected) => selected.id === project.id) &&
      !project.is_archived // Exclude archived projects
  );

  const addProject = (project: Project) => {
    const newProject: SelectedTeamProject = {
      id: project.id,
      name: project.name,
      description: project.description,
      status:
        typeof project.status === 'string'
          ? project.status
          : project.status?.name || 'unknown',
    };
    onProjectsChange([...selectedProjects, newProject]);
    setOpen(false);
  };

  const removeProject = (projectId: string) => {
    onProjectsChange(
      selectedProjects.filter((project) => project.id !== projectId)
    );
  };

  const getProjectLead = (project: Project) => {
    if (!project.lead_id) return null;
    return members.find((member) => member.id === project.lead_id);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'planning':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'on_hold':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatStatus = (status: string) => {
    return status
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Hash className='h-4 w-4' />
          <span className='text-sm font-medium'>Associated Projects</span>
          {selectedProjects.length > 0 && (
            <Badge variant='secondary' className='text-xs'>
              {selectedProjects.length}
            </Badge>
          )}
        </div>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              size='sm'
              disabled={disabled}
              className='h-8'
            >
              <Plus className='h-4 w-4 mr-2' />
              Add Project
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-96 p-0' align='end'>
            <Command>
              <CommandInput placeholder='Search projects...' />
              <CommandList>
                <CommandEmpty>No projects found.</CommandEmpty>
                <CommandGroup>
                  {availableProjects.map((project) => {
                    const lead = getProjectLead(project);
                    return (
                      <CommandItem
                        key={project.id}
                        onSelect={() => addProject(project)}
                        className='flex items-start gap-3 p-3'
                      >
                        <div className='flex-1 min-w-0 space-y-1'>
                          <div className='flex items-center gap-2'>
                            <p className='text-sm font-medium truncate'>
                              {project.name}
                            </p>
                            <Badge
                              variant='outline'
                              className={`text-xs ${getStatusColor(
                                typeof project.status === 'string'
                                  ? project.status
                                  : project.status?.name || 'unknown'
                              )}`}
                            >
                              {formatStatus(
                                typeof project.status === 'string'
                                  ? project.status
                                  : project.status?.name || 'unknown'
                              )}
                            </Badge>
                          </div>
                          {project.description && (
                            <p className='text-xs text-muted-foreground truncate'>
                              {project.description}
                            </p>
                          )}
                          <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                            {lead && (
                              <div className='flex items-center gap-1'>
                                <User className='h-3 w-3' />
                                <span>{lead.full_name || lead.name}</span>
                              </div>
                            )}
                            {project.target_date && (
                              <div className='flex items-center gap-1'>
                                <Calendar className='h-3 w-3' />
                                <span>
                                  {new Date(
                                    project.target_date
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {selectedProjects.length === 0 ? (
        <div className='text-center py-6 text-muted-foreground'>
          <Hash className='h-8 w-8 mx-auto mb-2 opacity-50' />
          <p className='text-sm'>No projects selected</p>
          <p className='text-xs'>
            Associate projects with this team to organize work
          </p>
        </div>
      ) : (
        <div className='space-y-2'>
          {selectedProjects.map((project) => {
            const fullProject = projects.find((p) => p.id === project.id);
            const lead = fullProject ? getProjectLead(fullProject) : null;

            return (
              <div
                key={project.id}
                className='flex items-center justify-between p-3 border bg-card hover:bg-accent/50 transition-colors'
              >
                <div className='flex-1 min-w-0 space-y-1'>
                  <div className='flex items-center gap-2'>
                    <p className='text-sm font-medium truncate'>
                      {project.name}
                    </p>
                    <Badge
                      variant='outline'
                      className={`text-xs ${getStatusColor(project.status)}`}
                    >
                      {formatStatus(project.status)}
                    </Badge>
                  </div>
                  {project.description && (
                    <p className='text-xs text-muted-foreground truncate'>
                      {project.description}
                    </p>
                  )}
                  <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                    {lead && (
                      <div className='flex items-center gap-1'>
                        <Avatar className='h-4 w-4'>
                          <AvatarImage src={lead.avatar_url || ''} />
                          <AvatarFallback className='text-xs'>
                            {(lead.full_name || lead.name)
                              .charAt(0)
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{lead.full_name || lead.name}</span>
                      </div>
                    )}
                    {fullProject?.target_date && (
                      <div className='flex items-center gap-1'>
                        <Calendar className='h-3 w-3' />
                        <span>
                          {new Date(
                            fullProject.target_date
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => removeProject(project.id)}
                  disabled={disabled}
                  className='h-7 w-7 p-0 text-muted-foreground hover:text-destructive'
                >
                  <X className='h-3 w-3' />
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
