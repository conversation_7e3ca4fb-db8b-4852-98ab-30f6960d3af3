'use client';

import { ColumnDef } from '@tanstack/react-table';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DatePicker } from '@/components/tasks/date-picker';
import { TaskStatusSelector } from '@/components/tasks/task-status-selector';
import { useTasks } from '@/hooks/use-db';
import type { Task } from '@/lib/supabase/database-modules';

export const columns: ColumnDef<Task>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      const task = row.original;
      return (
        <div className='flex flex-col gap-1'>
          <span className='font-medium'>{task.title}</span>
          {task.description && (
            <span className='text-sm text-muted-foreground line-clamp-2'>
              {task.description}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'project',
    header: 'Project',
    cell: ({ row }) => {
      const task = row.original;
      const project = task.project;

      if (!project) {
        return <span className='text-muted-foreground'>No project</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='font-medium'>{project.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const task = row.original;
      const { updateTask } = useTasks();

      const handleStatusChange = (status: Task['status']) => {
        updateTask(task.id, { status })
          .then(() => {
            toast.success('Task status updated');
          })
          .catch((error) => {
            toast.error('Failed to update task status');
            console.error('Error updating task status:', error);
          });
      };

      return (
        <TaskStatusSelector
          status={task.status}
          onStatusChange={handleStatusChange}
        />
      );
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const task = row.original;

      const priorityColors = {
        low: 'bg-blue-100 text-blue-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800',
      };

      return (
        <Badge className={priorityColors[task.priority]}>{task.priority}</Badge>
      );
    },
  },
  {
    accessorKey: 'assigned_to',
    header: 'Assigned To',
    cell: ({ row }) => {
      const task = row.original;
      const assignedUser = task.assigned_user;

      if (!assignedUser) {
        return <span className='text-muted-foreground'>Unassigned</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <Avatar className='size-6'>
            <AvatarImage
              src={assignedUser.avatar_url || undefined}
              alt={assignedUser.full_name || assignedUser.email}
            />
            <AvatarFallback>
              {assignedUser.email[0] || 'UN'.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm font-medium'>{assignedUser.full_name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const task = row.original;
      const { updateTask } = useTasks();

      const handleDateChange = (date: Date | null) => {
        const dateString = date ? date.toISOString() : null;
        updateTask(task.id, { due_date: dateString })
          .then(() => {
            toast.success('Task due date updated');
          })
          .catch((error) => {
            toast.error('Failed to update task due date');
            console.error('Error updating task due date:', error);
          });
      };

      return (
        <DatePicker
          date={task.due_date ? new Date(task.due_date) : null}
          onDateChange={handleDateChange}
        />
      );
    },
  },
  {
    accessorKey: 'actual_hours',
    header: 'Actual Hours',
    cell: ({ row }) => {
      const task = row.original;
      const { updateTask } = useTasks();

      const handleHoursChange = (hours: number | null) => {
        updateTask(task.id, { actual_hours: hours })
          .then(() => {
            toast.success('Task hours updated');
          })
          .catch((error) => {
            toast.error('Failed to update task hours');
            console.error('Error updating task hours:', error);
          });
      };

      return (
        <div className='flex items-center gap-2'>
          <input
            type='number'
            min='0'
            step='0.5'
            value={task.actual_hours || ''}
            onChange={(e) => {
              const value = e.target.value;
              handleHoursChange(value ? parseFloat(value) : null);
            }}
            className='w-20 px-2 py-1 text-sm border rounded'
            placeholder='0'
          />
          <span className='text-xs text-muted-foreground'>hrs</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_by',
    header: 'Created By',
    cell: ({ row }) => {
      const task = row.original;
      const creator = task.created_by_user;

      if (!creator) {
        return <span className='text-muted-foreground'>Unknown</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='text-sm'>{creator.full_name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const task = row.original;
      const createdDate = new Date(task.created_at);

      return (
        <span className='text-sm text-muted-foreground'>
          {createdDate.toLocaleDateString()}
        </span>
      );
    },
  },
];
