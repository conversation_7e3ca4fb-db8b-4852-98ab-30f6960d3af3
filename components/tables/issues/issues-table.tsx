'use client';

import { useState } from 'react';

import { DataTable } from '@/components/ui/data-table';
import { useIssues } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

import { createColumns } from './columns';

export function IssuesTable() {
  const { issues, loading, error } = useIssues();
  const [selectedIssue, setSelectedIssue] = useState<Issue | null>(null);

  const handleViewIssue = (issue: Issue) => {
    setSelectedIssue(issue);
    // Could open a dialog or navigate to issue detail page
  };

  const columns = createColumns({
    onViewIssue: handleViewIssue,
  });

  if (loading) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-muted-foreground'>Loading issues...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return <DataTable columns={columns} data={issues} />;
}
