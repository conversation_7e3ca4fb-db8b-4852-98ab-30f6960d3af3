'use client';

import { useMemo } from 'react';
import { MembersMetrics } from '@/components/members/members-metrics';
import { MembersTable } from '@/components/tables/members/members-table';
import { useMembers } from '@/hooks/use-db';

export default function Page() {
  const { members, loading } = useMembers();

  // Filter for affiliates only
  const affiliates = useMemo(() => {
    return members.filter((member) => member.role === 'Affiliate');
  }, [members]);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Affiliates</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <MembersMetrics members={affiliates} loading={loading} />
        <MembersTable filteredMembers={affiliates} />
      </section>
    </main>
  );
}
