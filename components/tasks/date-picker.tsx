'use client';

import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface DatePickerProps {
  date: Date | null;
  onDateChange?: (date: Date | null) => void;
}

export function DatePicker({ date, onDateChange }: DatePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    date || undefined
  );
  const [open, setOpen] = React.useState<boolean>(false);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (onDateChange) {
      onDateChange(date || null);
    }
    setOpen(false);
  };

  const handleClearDate = () => {
    setSelectedDate(undefined);
    if (onDateChange) {
      onDateChange(null);
    }
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='ghost'
          className='h-7 px-2 justify-start text-left font-normal'
          size='sm'
        >
          <CalendarIcon className='h-4 w-4 md:mr-0.5' />
          {selectedDate ? (
            <span className='text-xs hidden xl:inline mt-[1px]'>
              {format(selectedDate, 'MMM dd, yyyy')}
            </span>
          ) : (
            <span className='text-xs text-muted-foreground hidden xl:inline mt-[1px]'>
              No date
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0' align='start'>
        <Calendar
          mode='single'
          selected={selectedDate}
          onSelect={handleDateSelect}
          initialFocus
        />
        {selectedDate && (
          <div className='p-3 border-t'>
            <Button
              variant='outline'
              size='sm'
              onClick={handleClearDate}
              className='w-full'
            >
              Clear date
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
}
