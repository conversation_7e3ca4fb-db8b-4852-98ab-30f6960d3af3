'use client';

import { Badge } from '@/components/ui/badge';
import type { Member } from '@/lib/supabase/database-modules';

interface MemberStatusDisplayProps {
  member: Member;
}

const getStatusBadgeVariant = (status: string | null) => {
  switch (status) {
    case 'online':
      return 'default';
    case 'away':
      return 'secondary';
    case 'offline':
      return 'outline';
    default:
      return 'outline';
  }
};

const getStatusColor = (status: string | null) => {
  switch (status) {
    case 'online':
      return 'bg-green-500';
    case 'away':
      return 'bg-yellow-500';
    case 'offline':
      return 'bg-gray-500';
    default:
      return 'bg-gray-500';
  }
};

export function MemberStatusDisplay({ member }: MemberStatusDisplayProps) {
  const currentStatus = member.status || 'offline';

  return (
    <div className='flex items-center gap-2'>
      <div
        className={`w-2 h-2 rounded-full ${getStatusColor(currentStatus)}`}
      />
      <Badge
        variant={getStatusBadgeVariant(currentStatus)}
        className='capitalize'
      >
        {currentStatus}
      </Badge>
    </div>
  );
}
