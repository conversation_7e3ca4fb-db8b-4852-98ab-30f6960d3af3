'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  CircleCheck,
  CircleX,
  AlertCircle,
  HelpCircle,
  Bell,
  Check,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface ProjectHealth {
  id: string;
  name: string;
  description?: string | null;
}

interface ProjectData {
  id: string;
  name: string;
  description?: string | null;
  health?: ProjectHealth;
  lead?: {
    id: string;
    name: string;
    avatar_url?: string | null;
  };
}

interface HealthPopoverProps {
  project: ProjectData;
  onHealthChange?: (healthId: string) => void;
}

// Health status options
const healthStatuses = [
  {
    id: 'on-track',
    name: 'On Track',
    description: 'Project is progressing as planned',
    color: '#10b981',
  },
  {
    id: 'off-track',
    name: 'Off Track',
    description: 'Project is behind schedule or facing issues',
    color: '#ef4444',
  },
  {
    id: 'at-risk',
    name: 'At Risk',
    description: 'Project may face delays or issues',
    color: '#f59e0b',
  },
  {
    id: 'no-update',
    name: 'No Update',
    description: 'No recent status update available',
    color: '#6b7280',
  },
];

export function HealthPopover({ project, onHealthChange }: HealthPopoverProps) {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();

  const getHealthIcon = (healthId: string) => {
    switch (healthId) {
      case 'on-track':
        return <CircleCheck className='size-4 text-green-500' />;
      case 'off-track':
        return <CircleX className='size-4 text-red-500' />;
      case 'at-risk':
        return <AlertCircle className='size-4 text-amber-500' />;
      case 'no-update':
      default:
        return <HelpCircle className='size-4 text-muted-foreground' />;
    }
  };

  const handleHealthChange = (healthId: string) => {
    if (onHealthChange) {
      onHealthChange(healthId);
    }
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          className='flex items-center justify-center gap-1 h-7 px-2'
          size='sm'
          variant='ghost'
        >
          {getHealthIcon(project.health?.id || 'no-update')}
          <span className='text-xs mt-[1px] ml-0.5 hidden xl:inline'>
            {project.health?.name || 'No update'}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side={isMobile ? 'bottom' : 'left'}
        className={cn('p-0 w-[480px]', isMobile ? 'w-full' : '')}
      >
        <div className='flex items-center justify-between border-b p-3'>
          <div className='flex items-center gap-2'>
            <h4 className='font-medium text-sm'>{project.name}</h4>
          </div>
          <div className='flex items-center gap-2'>
            <Button variant='ghost' size='sm' className='h-7 px-2 text-xs'>
              Subscribe
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='h-7 px-2 text-xs flex items-center gap-1'
            >
              <Bell className='size-3' />
              New update
            </Button>
          </div>
        </div>
        <div className='p-3 space-y-3'>
          <div className='flex items-center justify-start gap-3'>
            <div className='flex items-center gap-2'>
              {getHealthIcon(project.health?.id || 'no-update')}
              <span className='text-sm'>
                {project.health?.name || 'No update'}
              </span>
            </div>
            {project.lead && (
              <div className='flex items-center gap-2'>
                <Avatar className='size-5'>
                  <AvatarImage
                    src={project.lead.avatar_url || undefined}
                    alt={project.lead.name}
                  />
                  <AvatarFallback>{project.lead.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span className='text-xs text-muted-foreground'>
                  {project.lead.name}
                </span>
              </div>
            )}
          </div>

          <div>
            <p className='text-sm text-muted-foreground'>
              {project.health?.description || 'No description available'}
            </p>
          </div>

          {/* Health Status Options */}
          {onHealthChange && (
            <div className='border-t pt-3'>
              <p className='text-xs font-medium text-muted-foreground mb-2'>
                Update Health Status
              </p>
              <div className='space-y-1'>
                {healthStatuses.map((status) => (
                  <Button
                    key={status.id}
                    variant='ghost'
                    size='sm'
                    className='w-full justify-between text-xs h-8'
                    onClick={() => handleHealthChange(status.id)}
                  >
                    <div className='flex items-center gap-2'>
                      {getHealthIcon(status.id)}
                      <span>{status.name}</span>
                    </div>
                    {project.health?.id === status.id && (
                      <Check className='h-3 w-3' />
                    )}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
