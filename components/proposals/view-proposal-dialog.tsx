'use client';

import { format } from 'date-fns';
import { useEffect, useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { supabaseClient } from '@/lib/supabase/auth/client';
import type { Proposal } from '@/lib/supabase/database-modules';

interface ViewProposalDialogProps {
  proposal: Proposal | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UserProfile {
  id: string;
  full_name: string | null;
  email: string;
  avatar_url: string | null;
}

const getApprovalStatusBadge = (status: boolean | null) => {
  if (status === true) {
    return (
      <Badge
        variant='default'
        className='bg-green-100 text-green-800 hover:bg-green-100'
      >
        Approved
      </Badge>
    );
  }
  if (status === false) {
    return <Badge variant='destructive'>Rejected</Badge>;
  }
  return <Badge variant='secondary'>Pending</Badge>;
};

export function ViewProposalDialog({
  proposal,
  open,
  onOpenChange,
}: ViewProposalDialogProps) {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loadingUser, setLoadingUser] = useState(false);

  // Fetch user profile when proposal changes
  useEffect(() => {
    if (!proposal?.user_id) {
      setUserProfile(null);
      return;
    }

    setLoadingUser(true);
    supabaseClient
      .from('profiles')
      .select('id, full_name, email, avatar_url')
      .eq('id', proposal.user_id)
      .single()
      .then(({ data, error }) => {
        if (error) {
          console.error('Error fetching user profile:', error);
          setUserProfile(null);
        } else {
          setUserProfile(data as UserProfile);
        }
        setLoadingUser(false);
      });
  }, [proposal?.user_id]);

  if (!proposal) return null;

  const clientName =
    proposal.client_name ||
    proposal.affiliate_proposal?.client_name ||
    'Unknown Client';
  const clientEmail = proposal.client_email || 'Not provided';
  const clientPhone = proposal.client_phone || 'Not provided';
  const clientDescription =
    proposal.client_description || 'No description provided';
  const proposalType =
    proposal.affiliate_proposal?.proposal_type || 'Unknown Type';
  const proposalMessage =
    proposal.affiliate_proposal?.proposal_message || 'No message provided';

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl max-h-[80vh] p-0'>
        <DialogHeader className='p-4'>
          <DialogTitle>Proposal Details</DialogTitle>
          <DialogDescription>
            View comprehensive details for this client proposal
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className='max-h-[60vh] p-4'>
          <div className='space-y-6'>
            {/* Basic Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Basic Information</h3>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Client Name
                  </div>
                  <p className='text-sm font-medium mt-1'>{clientName}</p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Client Email
                  </div>
                  <p className='text-sm font-medium mt-1'>{clientEmail}</p>
                </div>
              </div>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Client Phone
                  </div>
                  <p className='text-sm font-medium mt-1'>{clientPhone}</p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Proposal Type
                  </div>
                  <p className='text-sm font-medium mt-1'>{proposalType}</p>
                </div>
              </div>

              {clientDescription !== 'No description provided' && (
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Client Description
                  </div>
                  <p className='text-sm mt-1 bg-muted/30 rounded-md p-3 whitespace-pre-wrap'>
                    {clientDescription}
                  </p>
                </div>
              )}

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Submitted Date
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {format(
                      new Date(proposal.created_at),
                      "MMMM dd, yyyy 'at' HH:mm"
                    )}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Approval Status
                  </div>
                  <div className='mt-1'>
                    {getApprovalStatusBadge(proposal.is_approved)}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Contact Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Contact Information</h3>

              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    User Profile
                  </div>
                  {loadingUser ? (
                    <div className='flex items-center gap-2 mt-1'>
                      <div className='w-8 h-8 bg-muted rounded-full animate-pulse' />
                      <div className='text-sm text-muted-foreground'>
                        Loading...
                      </div>
                    </div>
                  ) : userProfile ? (
                    <div className='flex items-center gap-2 mt-1'>
                      <Avatar className='w-8 h-8'>
                        <AvatarImage
                          src={userProfile.avatar_url || undefined}
                        />
                        <AvatarFallback>
                          {userProfile.full_name
                            ? userProfile.full_name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')
                                .toUpperCase()
                            : userProfile.email[0].toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className='text-sm font-medium'>
                          {userProfile.full_name || 'No name provided'}
                        </p>
                        {/* <p className='text-xs text-muted-foreground'>
                          ID: {userProfile.id}
                        </p> */}
                      </div>
                    </div>
                  ) : (
                    <p className='text-sm text-muted-foreground mt-1'>
                      No user profile found
                    </p>
                  )}
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Email Address
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {proposal.user_email || 'Not provided'}
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Proposal Message */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Proposal Message</h3>
              <div className='bg-muted/50 rounded-lg p-4'>
                <p className='text-sm whitespace-pre-wrap leading-relaxed'>
                  {proposalMessage}
                </p>
              </div>
            </div>

            <Separator />

            {/* Status Information */}
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>Status Information</h3>

              <div className='grid grid-cols-3 gap-4'>
                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Received
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {proposal.is_recieved ? 'Yes' : 'No'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Approved
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {proposal.is_approved === null
                      ? 'Pending'
                      : proposal.is_approved
                        ? 'Yes'
                        : 'No'}
                  </p>
                </div>

                <div>
                  <div className='text-sm font-medium text-muted-foreground'>
                    Completed
                  </div>
                  <p className='text-sm font-medium mt-1'>
                    {proposal.completed ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
